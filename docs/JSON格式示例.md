# SmartKid终端 JSON格式示例

## 📋 概述

本文档提供SmartKid终端JSON协议的具体示例，展示各种场景下的消息格式。

## 🎵 音乐控制示例

### 接收格式（云端 → 终端）

```json
{
  "code": 2200,
  "data": {
    "action": "music",
    "operate": "start",
    "text": "播放音乐"
  },
  "message": "SUCCEED",
  "sessionId": "music_session_001",
  "sn": 1
}
```

### 发送格式（终端 → 云端）

```json
{
  "action": "music",
  "operate": "start",
  "requestId": "1",
  "timestamp": 1753774119,
  "body": "Voice command play music"
}
```

## 📚 故事播放示例

### 故事播放

**接收格式：**
```json
{
  "code": 2200,
  "data": {
    "action": "story",
    "operate": "start",
    "text": "讲故事"
  },
  "message": "SUCCEED",
  "sessionId": "story_session_001",
  "sn": 2
}
```

**发送格式：**
```json
{
  "action": "story",
  "operate": "start",
  "requestId": "2",
  "timestamp": 1753774120,
  "body": "Story request"
}
```

## 📞 通话控制示例

### 发起通话

**接收格式：**
```json
{
  "code": 2200,
  "data": {
    "action": "call",
    "operate": "handshake",
    "text": "发起通话"
  },
  "message": "SUCCEED",
  "sessionId": "call_session_001",
  "sn": 5
}
```

### 接受通话

**发送格式：**
```json
{
  "action": "call",
  "operate": "accept",
  "requestId": "3",
  "timestamp": 1753774121,
  "body": "Accept incoming call"
}
```

## 🎛️ 命令控制示例

### 音量控制

**接收格式：**
```json
{
  "code": 2200,
  "data": {
    "action": "cmd",
    "operate": "vol_up",
    "text": "音量增加"
  },
  "message": "SUCCEED",
  "sessionId": "cmd_session_001",
  "sn": 6
}
```

### 通用控制命令

**接收格式：**
```json
{
  "code": 2200,
  "data": {
    "action": "cmd",
    "operate": "pause",
    "text": "暂停"
  },
  "message": "SUCCEED",
  "sessionId": "cmd_session_002",
  "sn": 7
}
```

**发送格式：**
```json
{
  "action": "cmd",
  "operate": "vol_up",
  "requestId": "4",
  "timestamp": 1753774122,
  "body": "Volume up command"
}
```

## ⚠️ 错误处理示例

### 错误响应

```json
{
  "code": 4000,
  "data": {
    "action": "music",
    "operate": "start",
    "text": "播放失败"
  },
  "message": "ERROR",
  "sessionId": "error_session_001",
  "sn": 6
}
```

## 🔄 处理逻辑说明

### Action 映射关系

| 接收Action | 处理函数 | 说明 |
|------------|----------|------|
| `music` | `ProcessMusicCommand` | 音乐控制 |
| `story` | `ProcessStoryCommand` | 故事播放 |
| `call` | `ProcessCallCommand` | 通话控制 |
| `cmd` | `ProcessCmdCommand` | 命令控制 |

### 状态机事件映射

| 操作类型 | 状态机事件 | 说明 |
|----------|------------|------|
| 音乐开始 | `SPEECH_CMD_EVENT_MUSIC` | 切换到音乐状态 |
| 故事开始 | 待定义 | 切换到故事状态 |
| 通话握手 | `SPEECH_CMD_EVENT_CALL` | 切换到通话状态 |
| 暂停 | `SPEECH_CMD_EVENT_PAUSE` | 暂停当前操作 |
| 恢复 | `SPEECH_CMD_EVENT_RESUME` | 恢复当前操作 |
| 停止 | `SPEECH_CMD_EVENT_QUIT` | 退出当前状态 |
| 下一个 | `SPEECH_CMD_EVENT_NEXT` | 下一个内容 |
| 上一个 | `SPEECH_CMD_EVENT_PREV` | 上一个内容 |
| 音量控制 | 待定义 | 音量调节（需要音频模块接口） |

## 🧪 测试命令

### 使用C代码测试

```c
// 测试音乐控制
SkRlinkSendMusicCommand("start", "Test music");
SkRlinkSendMusicCommand("pause", "Pause music");
SkRlinkSendMusicCommand("stop", "Stop music");

// 测试故事播放
SkRlinkSendStoryCommand("start", "Test story");
SkRlinkSendStoryCommand("next", "Next story");

// 测试通话
SkRlinkSendCallCommand("handshake", "Start call");
SkRlinkSendCallCommand("accept", "Accept call");

// 测试命令控制
SkRlinkSendCmdCommand("vol_up", "Volume up");
SkRlinkSendCmdCommand("pause", "Pause command");
SkRlinkSendCmdCommand("start_music", "Start music via cmd");
```

## 📝 注意事项

1. **统一处理**：所有故事相关功能都在 `ProcessStoryCommand` 中处理
2. **状态机集成**：故事播放会触发相应的状态机事件
3. **扩展性**：可以根据 `text` 字段内容选择不同的故事内容

## 🔧 代码优化

通过简化 action 类型，实现了：

- ✅ **代码简化**：减少了不必要的处理分支
- ✅ **逻辑清晰**：每个 action 对应明确的功能
- ✅ **易于维护**：统一的处理逻辑便于后续扩展
