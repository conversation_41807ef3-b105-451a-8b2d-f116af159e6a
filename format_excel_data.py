#!/usr/bin/env python3
"""
格式化Excel数据为表格
"""
import zipfile
import xml.etree.ElementTree as ET
import sys
import os

def read_excel_as_table(file_path):
    """读取Excel文件并格式化为表格"""
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            # 读取共享字符串
            shared_strings = []
            try:
                with zip_file.open('xl/sharedStrings.xml') as f:
                    content = f.read().decode('utf-8')
                    root = ET.fromstring(content)
                    
                    for si in root.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}si'):
                        t_elem = si.find('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}t')
                        if t_elem is not None:
                            shared_strings.append(t_elem.text or '')
                        else:
                            shared_strings.append('')
                            
            except KeyError:
                print("没有找到共享字符串文件")
            
            # 读取工作表数据
            try:
                with zip_file.open('xl/worksheets/sheet1.xml') as f:
                    content = f.read().decode('utf-8')
                    root = ET.fromstring(content)
                    
                    # 创建表格数据结构
                    table_data = {}
                    max_col = 0
                    max_row = 0
                    
                    # 查找所有行
                    for row in root.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}row'):
                        row_num = int(row.get('r', '0'))
                        max_row = max(max_row, row_num)
                        
                        # 查找行中的所有单元格
                        for cell in row.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}c'):
                            cell_ref = cell.get('r', '')
                            cell_type = cell.get('t', '')
                            
                            # 解析列号
                            col_str = ''.join([c for c in cell_ref if c.isalpha()])
                            col_num = 0
                            for c in col_str:
                                col_num = col_num * 26 + (ord(c) - ord('A') + 1)
                            max_col = max(max_col, col_num)
                            
                            # 获取单元格值
                            v_elem = cell.find('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}v')
                            if v_elem is not None:
                                value = v_elem.text or ''
                                
                                # 如果是共享字符串类型，从共享字符串表中获取实际值
                                if cell_type == 's' and value.isdigit():
                                    idx = int(value)
                                    if 0 <= idx < len(shared_strings):
                                        value = shared_strings[idx]
                                
                                table_data[(row_num, col_num)] = value
                    
                    # 打印表格
                    print("Action List 表格内容:")
                    print("=" * 100)
                    
                    # 打印表头
                    headers = []
                    for col in range(1, max_col + 1):
                        cell_value = table_data.get((1, col), '')
                        headers.append(cell_value)
                    
                    # 格式化表头
                    col_widths = [15, 15, 15, 10, 10, 30]  # 列宽
                    header_line = ""
                    for i, header in enumerate(headers):
                        if i < len(col_widths):
                            header_line += f"{header:<{col_widths[i]}}"
                        else:
                            header_line += f"{header:<15}"
                    print(header_line)
                    print("-" * 100)
                    
                    # 打印数据行
                    for row in range(2, max_row + 1):
                        row_data = []
                        for col in range(1, max_col + 1):
                            cell_value = table_data.get((row, col), '')
                            row_data.append(cell_value)
                        
                        # 只打印非空行
                        if any(cell.strip() for cell in row_data):
                            row_line = ""
                            for i, cell in enumerate(row_data):
                                if i < len(col_widths):
                                    row_line += f"{cell:<{col_widths[i]}}"
                                else:
                                    row_line += f"{cell:<15}"
                            print(row_line)
                        
            except KeyError:
                print("没有找到工作表文件")
                
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return False
    
    return True

if __name__ == "__main__":
    file_path = "/opt/Amor/work/sk-terminal_1/action_list(1).xlsx"
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        sys.exit(1)
    
    success = read_excel_as_table(file_path)
    if not success:
        sys.exit(1)
