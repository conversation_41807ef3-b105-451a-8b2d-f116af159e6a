/**
 * @file: test_new_json_format.c
 * @description: 测试新JSON格式的处理
 * @author: AI Assistant
 * @date: 2025-01-08
 */

#include <stdio.h>
#include <string.h>
#include "sk_rlink.h"
#include "sk_log.h"

static const char *TAG = "JsonTest";

/**
 * @brief 测试接收JSON格式的示例
 */
void TestReceiveJsonFormat() {
    SK_LOGI(TAG, "=== 测试接收JSON格式 ===");
    
    // 测试音乐开始命令
    const char *musicStartJson = 
        "{"
        "\"code\": 2200,"
        "\"data\": {"
        "\"action\": \"music\","
        "\"operate\": \"start\","
        "\"text\": \"播放音乐\""
        "},"
        "\"message\": \"SUCCEED\","
        "\"sessionId\": \"b0134564179e486589ba90211ed74071\","
        "\"sn\": 1"
        "}";
    
    SK_LOGI(TAG, "测试音乐开始命令:");
    SK_LOGI(TAG, "%s", musicStartJson);
    
    // 测试故事开始命令
    const char *storyStartJson = 
        "{"
        "\"code\": 2200,"
        "\"data\": {"
        "\"action\": \"mock_story\","
        "\"operate\": \"start\","
        "\"text\": \"听AI讲故事\""
        "},"
        "\"message\": \"SUCCEED\","
        "\"sessionId\": \"b0134564179e486589ba90211ed74072\","
        "\"sn\": 2"
        "}";
    
    SK_LOGI(TAG, "测试故事开始命令:");
    SK_LOGI(TAG, "%s", storyStartJson);
    
    // 测试通话握手命令
    const char *callHandshakeJson = 
        "{"
        "\"code\": 2200,"
        "\"data\": {"
        "\"action\": \"call\","
        "\"operate\": \"handshake\","
        "\"text\": \"发起通话\""
        "},"
        "\"message\": \"SUCCEED\","
        "\"sessionId\": \"b0134564179e486589ba90211ed74073\","
        "\"sn\": 3"
        "}";
    
    SK_LOGI(TAG, "测试通话握手命令:");
    SK_LOGI(TAG, "%s", callHandshakeJson);
    
    // 测试错误码
    const char *errorJson = 
        "{"
        "\"code\": 4000,"
        "\"data\": {"
        "\"action\": \"music\","
        "\"operate\": \"start\","
        "\"text\": \"播放失败\""
        "},"
        "\"message\": \"ERROR\","
        "\"sessionId\": \"b0134564179e486589ba90211ed74074\","
        "\"sn\": 4"
        "}";
    
    SK_LOGI(TAG, "测试错误响应:");
    SK_LOGI(TAG, "%s", errorJson);
}

/**
 * @brief 测试发送JSON格式的示例
 */
void TestSendJsonFormat() {
    SK_LOGI(TAG, "=== 测试发送JSON格式 ===");
    
    // 测试音乐控制命令
    SK_LOGI(TAG, "发送音乐开始命令:");
    SkRlinkSendMusicCommand("start", "开始播放音乐");
    
    SK_LOGI(TAG, "发送音乐暂停命令:");
    SkRlinkSendMusicCommand("pause", "暂停音乐");
    
    SK_LOGI(TAG, "发送音乐停止命令:");
    SkRlinkSendMusicCommand("stop", "停止音乐");
    
    // 测试故事控制命令
    SK_LOGI(TAG, "发送故事开始命令:");
    SkRlinkSendStoryCommand("start", "开始讲故事");
    
    SK_LOGI(TAG, "发送故事下一个命令:");
    SkRlinkSendStoryCommand("next", "下一个故事");
    
    // 测试通话控制命令
    SK_LOGI(TAG, "发送通话握手命令:");
    SkRlinkSendCallCommand("handshake", "发起通话");
    
    SK_LOGI(TAG, "发送通话接受命令:");
    SkRlinkSendCallCommand("accept", "接受通话");
    
    // 测试通用命令
    SK_LOGI(TAG, "发送测试命令:");
    SkRlinkSendTestCommand("start", "这是一个测试消息");
    
    // 测试自定义命令
    SK_LOGI(TAG, "发送自定义命令:");
    SkRlinkSendJsonMessage("custom", "test", "自定义测试消息");
}

/**
 * @brief 显示JSON格式说明
 */
void ShowJsonFormatDescription() {
    SK_LOGI(TAG, "=== JSON格式说明 ===");
    
    SK_LOGI(TAG, "接收格式 (从云端接收):");
    SK_LOGI(TAG, "{");
    SK_LOGI(TAG, "  \"code\": 2200,                    // 状态码");
    SK_LOGI(TAG, "  \"data\": {");
    SK_LOGI(TAG, "    \"action\": \"music\",           // 动作类型");
    SK_LOGI(TAG, "    \"operate\": \"start\",          // 操作类型");
    SK_LOGI(TAG, "    \"text\": \"播放音乐\"           // ASR文本");
    SK_LOGI(TAG, "  },");
    SK_LOGI(TAG, "  \"message\": \"SUCCEED\",          // 响应消息");
    SK_LOGI(TAG, "  \"sessionId\": \"xxx\",            // 会话ID");
    SK_LOGI(TAG, "  \"sn\": 1                         // 序列号");
    SK_LOGI(TAG, "}");
    
    SK_LOGI(TAG, "");
    SK_LOGI(TAG, "发送格式 (发送到云端):");
    SK_LOGI(TAG, "{");
    SK_LOGI(TAG, "  \"action\": \"music\",             // 动作类型");
    SK_LOGI(TAG, "  \"operate\": \"start\",            // 操作类型");
    SK_LOGI(TAG, "  \"requestId\": \"12\",             // 请求ID");
    SK_LOGI(TAG, "  \"timestamp\": 1753774119,         // 时间戳(毫秒级)");
    SK_LOGI(TAG, "  \"body\": \"开始播放音乐\"         // 消息体");
    SK_LOGI(TAG, "}");
    
    SK_LOGI(TAG, "");
    SK_LOGI(TAG, "支持的action类型:");
    SK_LOGI(TAG, "- music: 音乐控制");
    SK_LOGI(TAG, "- story: 故事播放");
    SK_LOGI(TAG, "- call: 通话控制");
    SK_LOGI(TAG, "- mock_story: AI讲故事");
    SK_LOGI(TAG, "- test: 测试命令");
    
    SK_LOGI(TAG, "");
    SK_LOGI(TAG, "支持的operate类型:");
    SK_LOGI(TAG, "- start: 开始");
    SK_LOGI(TAG, "- pause: 暂停");
    SK_LOGI(TAG, "- resume: 恢复");
    SK_LOGI(TAG, "- stop: 停止");
    SK_LOGI(TAG, "- next: 下一个");
    SK_LOGI(TAG, "- prev: 上一个");
    SK_LOGI(TAG, "- handshake: 握手");
    SK_LOGI(TAG, "- accept: 接受");
    SK_LOGI(TAG, "- reject: 拒绝");
}

/**
 * @brief 主测试函数
 */
void TestNewJsonFormat() {
    SK_LOGI(TAG, "开始测试新JSON格式处理");
    SK_LOGI(TAG, "========================================");
    
    ShowJsonFormatDescription();
    SK_LOGI(TAG, "");
    
    TestReceiveJsonFormat();
    SK_LOGI(TAG, "");
    
    TestSendJsonFormat();
    SK_LOGI(TAG, "");
    
    SK_LOGI(TAG, "========================================");
    SK_LOGI(TAG, "新JSON格式测试完成");
}
