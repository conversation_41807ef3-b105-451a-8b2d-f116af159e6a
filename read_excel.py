#!/usr/bin/env python3
"""
读取Excel文件的脚本
"""
import zipfile
import xml.etree.ElementTree as ET
import sys
import os

def read_excel_xml(file_path):
    """读取Excel文件并解析XML内容"""
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            # 读取共享字符串
            shared_strings = []
            try:
                with zip_file.open('xl/sharedStrings.xml') as f:
                    content = f.read().decode('utf-8')
                    root = ET.fromstring(content)
                    
                    # 查找所有文本节点
                    for si in root.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}si'):
                        t_elem = si.find('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}t')
                        if t_elem is not None:
                            shared_strings.append(t_elem.text or '')
                        else:
                            shared_strings.append('')
                            
                print(f"共享字符串数量: {len(shared_strings)}")
                print("共享字符串内容:")
                for i, s in enumerate(shared_strings):
                    print(f"  {i}: {s}")
                print("=" * 50)
                            
            except KeyError:
                print("没有找到共享字符串文件")
            
            # 读取工作表数据
            try:
                with zip_file.open('xl/worksheets/sheet1.xml') as f:
                    content = f.read().decode('utf-8')
                    root = ET.fromstring(content)
                    
                    print("工作表数据:")
                    rows = []
                    
                    # 查找所有行
                    for row in root.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}row'):
                        row_data = []
                        row_num = row.get('r', '')
                        
                        # 查找行中的所有单元格
                        for cell in row.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}c'):
                            cell_ref = cell.get('r', '')
                            cell_type = cell.get('t', '')
                            
                            # 获取单元格值
                            v_elem = cell.find('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}v')
                            if v_elem is not None:
                                value = v_elem.text or ''
                                
                                # 如果是共享字符串类型，从共享字符串表中获取实际值
                                if cell_type == 's' and value.isdigit():
                                    idx = int(value)
                                    if 0 <= idx < len(shared_strings):
                                        value = shared_strings[idx]
                                
                                row_data.append((cell_ref, value))
                            else:
                                row_data.append((cell_ref, ''))
                        
                        if row_data:
                            rows.append((row_num, row_data))
                    
                    # 打印所有行数据
                    print(f"总行数: {len(rows)}")
                    for i, (row_num, row_data) in enumerate(rows):
                        print(f"行 {row_num}:")
                        for cell_ref, value in row_data:
                            if value.strip():  # 只显示非空值
                                print(f"  {cell_ref}: {value}")
                        print()
                        
            except KeyError:
                print("没有找到工作表文件")
                
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return False
    
    return True

if __name__ == "__main__":
    file_path = "/opt/Amor/work/sk-terminal_1/action_list(1).xlsx"
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        sys.exit(1)
    
    print(f"正在读取Excel文件: {file_path}")
    print("=" * 50)
    
    success = read_excel_xml(file_path)
    if not success:
        sys.exit(1)
