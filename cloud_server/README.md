# SmartKid Cloud Server

这是一个用Python编写的SmartKid云服务器，支持与SmartKid终端的JSON协议通信。

## 功能特性

- ✅ **WebSocket通信**：支持与SmartKid终端的实时双向通信
- ✅ **JSON协议解析**：完整支持SmartKid的JSON消息格式
- ✅ **多种Action支持**：music、story、call、cmd四种动作类型
- ✅ **自动响应**：智能处理终端请求并生成标准响应
- ✅ **定期命令发送**：可配置的定期测试命令发送
- ✅ **客户端管理**：支持多客户端连接和管理
- ✅ **错误处理**：完善的错误处理和日志记录
- ✅ **配置化**：灵活的配置选项

## 支持的协议

### 接收格式（终端 → 云端）
```json
{
  "action": "music|story|call|cmd|test",
  "operate": "start|pause|resume|stop|next|prev|...",
  "requestId": "请求ID",
  "timestamp": 1753774119,
  "body": "消息体内容"
}
```

### 发送格式（云端 → 终端）
```json
{
  "code": 2200,
  "data": {
    "action": "music|story|call|cmd",
    "operate": "start|pause|resume|stop|...",
    "text": "响应文本"
  },
  "message": "SUCCEED",
  "sessionId": "会话ID",
  "sn": 序列号
}
```

## 支持的操作

### 🎵 音乐控制 (music)
- `start` - 开始播放音乐
- `pause` - 暂停音乐
- `resume` - 恢复音乐
- `stop` - 停止音乐
- `next` - 下一首
- `prev` - 上一首

### 📚 故事播放 (story)
- `start` - 开始播放故事
- `pause` - 暂停故事
- `resume` - 恢复故事
- `stop` - 停止故事
- `next` - 下一个故事
- `prev` - 上一个故事

### 📞 通话控制 (call)
- `handshake` - 发起通话握手
- `accept` - 接受通话
- `reject` - 拒绝通话
- `start` - 开始通话
- `stop` - 结束通话

### 🎛️ 命令控制 (cmd)
- `vol_up` / `vol_down` - 音量控制
- `vol_max` / `vol_min` - 音量极值
- `pause` / `resume` - 暂停/恢复
- `exit` - 退出
- `next` / `prev` - 导航控制
- `start_music` / `stop_music` - 音乐控制
- `start_story` / `stop_story` - 故事控制
- `start_call` - 通话控制

## 安装和运行

### 1. 安装依赖
```bash
pip3 install -r requirements.txt
```

### 2. 启动服务器
```bash
# 方法1：使用启动脚本
./start_server.sh

# 方法2：直接运行
python3 smartkid_server.py

# 方法3：自定义配置
SMARTKID_HOST=0.0.0.0 SMARTKID_PORT=8765 python3 smartkid_server.py
```

### 3. 测试服务器
```bash
# 自动测试模式
python3 test_client.py

# 交互测试模式
python3 test_client.py interactive
```

## 配置选项

可以通过环境变量或修改 `config.py` 来配置服务器：

```bash
export SMARTKID_HOST="0.0.0.0"          # 服务器地址
export SMARTKID_PORT="8765"             # 服务器端口
export LOG_LEVEL="INFO"                 # 日志级别
```

## 使用示例

### 启动服务器
```bash
$ ./start_server.sh
Starting SmartKid Cloud Server...
Python version: Python 3.9.2
Installing dependencies...
Server configuration:
  Host: 0.0.0.0
  Port: 8765
  Log Level: INFO
Starting server...
2025-01-08 10:30:00,123 - SmartKidServer - INFO - Starting SmartKid server on 0.0.0.0:8765
2025-01-08 10:30:00,124 - SmartKidServer - INFO - SmartKid server is running on ws://0.0.0.0:8765
```

### 客户端连接示例
```python
import asyncio
import websockets
import json

async def test_connection():
    uri = "ws://localhost:8765"
    async with websockets.connect(uri) as websocket:
        # 发送音乐开始命令
        request = {
            "action": "music",
            "operate": "start",
            "requestId": "1",
            "timestamp": int(time.time() * 1000),
            "body": "Play music"
        }
        
        await websocket.send(json.dumps(request))
        response = await websocket.recv()
        print(f"Response: {response}")

asyncio.run(test_connection())
```

## 日志输出示例

```
2025-01-08 10:30:15,456 - SmartKidServer - INFO - Client client_1 connected from ('*************', 54321)
2025-01-08 10:30:20,789 - SmartKidServer - INFO - Received message: {'action': 'music', 'operate': 'start', 'requestId': '1', 'timestamp': 1753774119, 'body': 'Play music'}
2025-01-08 10:30:20,790 - SmartKidServer - INFO - Processing music command: start, body: Play music
2025-01-08 10:30:20,791 - SmartKidServer - INFO - Sent response to client_1: {'code': 2200, 'data': {'action': 'music', 'operate': 'start', 'text': '开始播放音乐'}, 'message': 'SUCCEED', 'sessionId': 'abc123', 'sn': 1}
```

## 文件结构

```
cloud_server/
├── smartkid_server.py    # 主服务器文件
├── test_client.py        # 测试客户端
├── config.py            # 配置文件
├── requirements.txt     # Python依赖
├── start_server.sh      # 启动脚本
└── README.md           # 说明文档
```

## 开发和扩展

### 添加新的Action类型
1. 在 `config.py` 中的 `SUPPORTED_OPERATIONS` 添加新的action
2. 在 `smartkid_server.py` 中添加对应的处理函数
3. 在 `process_message` 方法中添加分发逻辑

### 自定义响应逻辑
修改对应的 `handle_*_command` 方法来实现自定义的业务逻辑。

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查找占用端口的进程
   lsof -i :8765
   # 或者使用其他端口
   SMARTKID_PORT=8766 python3 smartkid_server.py
   ```

2. **依赖安装失败**
   ```bash
   # 升级pip
   pip3 install --upgrade pip
   # 重新安装依赖
   pip3 install -r requirements.txt
   ```

3. **连接被拒绝**
   - 检查服务器是否正在运行
   - 检查防火墙设置
   - 确认IP地址和端口配置正确

## 许可证

本项目仅供学习和测试使用。
