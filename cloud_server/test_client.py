#!/usr/bin/env python3
"""
SmartKid Test Client
用于测试SmartKid云服务器的功能
"""

import asyncio
import websockets
import json
import logging
import time
from typing import Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('TestClient')

class SmartKidTestClient:
    def __init__(self, server_url='ws://localhost:8765'):
        self.server_url = server_url
        self.request_id_counter = 1
        
    def create_request(self, action: str, operate: str, body: str = "") -> Dict[str, Any]:
        """创建请求消息"""
        request = {
            "action": action,
            "operate": operate,
            "requestId": str(self.request_id_counter),
            "timestamp": int(time.time() * 1000),  # 毫秒级时间戳
            "body": body
        }
        self.request_id_counter += 1
        return request
    
    async def send_test_commands(self, websocket):
        """发送测试命令"""
        test_commands = [
            # 音乐控制测试
            {'action': 'music', 'operate': 'start', 'body': 'Voice command play music'},
            {'action': 'music', 'operate': 'pause', 'body': 'Pause music'},
            {'action': 'music', 'operate': 'resume', 'body': 'Resume music'},
            {'action': 'music', 'operate': 'next', 'body': 'Next song'},
            {'action': 'music', 'operate': 'stop', 'body': 'Stop music'},
            
            # 故事控制测试
            {'action': 'story', 'operate': 'start', 'body': 'Start story'},
            {'action': 'story', 'operate': 'pause', 'body': 'Pause story'},
            {'action': 'story', 'operate': 'next', 'body': 'Next story'},
            {'action': 'story', 'operate': 'stop', 'body': 'Stop story'},
            
            # 通话控制测试
            {'action': 'call', 'operate': 'handshake', 'body': 'Start call handshake'},
            {'action': 'call', 'operate': 'accept', 'body': 'Accept call'},
            {'action': 'call', 'operate': 'stop', 'body': 'End call'},
            
            # 命令控制测试
            {'action': 'cmd', 'operate': 'vol_up', 'body': 'Volume up'},
            {'action': 'cmd', 'operate': 'vol_down', 'body': 'Volume down'},
            {'action': 'cmd', 'operate': 'pause', 'body': 'Pause command'},
            {'action': 'cmd', 'operate': 'start_music', 'body': 'Start music via cmd'},
            
            # 测试命令
            {'action': 'test', 'operate': 'ping', 'body': 'Test message'},
            
            # 错误测试
            {'action': 'unknown', 'operate': 'test', 'body': 'Unknown action test'},
        ]
        
        for i, cmd in enumerate(test_commands):
            try:
                request = self.create_request(cmd['action'], cmd['operate'], cmd['body'])
                request_json = json.dumps(request, ensure_ascii=False)
                
                logger.info(f"Sending command {i+1}/{len(test_commands)}: {cmd['action']}.{cmd['operate']}")
                await websocket.send(request_json)
                
                # 等待响应
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    response_data = json.loads(response)
                    logger.info(f"Received response: code={response_data.get('code')}, "
                              f"message={response_data.get('message')}, "
                              f"action={response_data.get('data', {}).get('action')}")
                except asyncio.TimeoutError:
                    logger.warning("Response timeout")
                except json.JSONDecodeError as e:
                    logger.error(f"JSON decode error: {e}")
                
                await asyncio.sleep(2)  # 每2秒发送一个命令
                
            except Exception as e:
                logger.error(f"Error sending command: {e}")
                break
    
    async def listen_for_server_commands(self, websocket):
        """监听服务器发送的命令"""
        try:
            while True:
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                    data = json.loads(message)
                    
                    logger.info(f"Received server command: {data}")
                    
                    # 模拟终端响应
                    if data.get('code') == 2200:
                        action = data.get('data', {}).get('action', '')
                        operate = data.get('data', {}).get('operate', '')
                        
                        # 发送确认响应
                        response = self.create_request(action, operate, f"Confirmed {action}.{operate}")
                        response_json = json.dumps(response, ensure_ascii=False)
                        await websocket.send(response_json)
                        logger.info(f"Sent confirmation for {action}.{operate}")
                        
                except asyncio.TimeoutError:
                    continue
                except json.JSONDecodeError as e:
                    logger.error(f"JSON decode error: {e}")
                except Exception as e:
                    logger.error(f"Error receiving server command: {e}")
                    break
                    
        except Exception as e:
            logger.error(f"Error in listen_for_server_commands: {e}")
    
    async def run_interactive_mode(self, websocket):
        """交互模式"""
        logger.info("Entering interactive mode. Type commands or 'quit' to exit.")
        logger.info("Format: action operate body")
        logger.info("Example: music start Play my favorite song")
        
        while True:
            try:
                user_input = input("\nEnter command: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    break
                
                if not user_input:
                    continue
                
                parts = user_input.split(' ', 2)
                if len(parts) < 2:
                    print("Invalid format. Use: action operate [body]")
                    continue
                
                action = parts[0]
                operate = parts[1]
                body = parts[2] if len(parts) > 2 else ""
                
                request = self.create_request(action, operate, body)
                request_json = json.dumps(request, ensure_ascii=False)
                
                await websocket.send(request_json)
                logger.info(f"Sent: {action}.{operate}")
                
                # 等待响应
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    response_data = json.loads(response)
                    print(f"Response: {response_data}")
                except asyncio.TimeoutError:
                    print("Response timeout")
                except json.JSONDecodeError as e:
                    print(f"JSON decode error: {e}")
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                logger.error(f"Error in interactive mode: {e}")
                break
    
    async def connect_and_test(self, mode='auto'):
        """连接服务器并测试"""
        try:
            logger.info(f"Connecting to {self.server_url}")
            
            async with websockets.connect(self.server_url) as websocket:
                logger.info("Connected to SmartKid server")
                
                if mode == 'auto':
                    # 自动测试模式
                    listen_task = asyncio.create_task(self.listen_for_server_commands(websocket))
                    
                    await self.send_test_commands(websocket)
                    
                    # 继续监听服务器命令
                    logger.info("Continuing to listen for server commands...")
                    await asyncio.sleep(30)  # 监听30秒
                    
                    listen_task.cancel()
                    
                elif mode == 'interactive':
                    # 交互模式
                    listen_task = asyncio.create_task(self.listen_for_server_commands(websocket))
                    
                    await self.run_interactive_mode(websocket)
                    
                    listen_task.cancel()
                
        except websockets.exceptions.ConnectionRefused:
            logger.error(f"Cannot connect to {self.server_url}. Is the server running?")
        except Exception as e:
            logger.error(f"Connection error: {e}")

def main():
    """主函数"""
    import sys
    
    mode = 'auto'
    if len(sys.argv) > 1:
        if sys.argv[1] in ['interactive', 'i']:
            mode = 'interactive'
    
    client = SmartKidTestClient()
    
    try:
        asyncio.run(client.connect_and_test(mode))
    except KeyboardInterrupt:
        logger.info("Test client stopped by user")
    except Exception as e:
        logger.error(f"Test client error: {e}")

if __name__ == "__main__":
    main()
