#!/usr/bin/env python3
"""
创建简单的测试音频文件
生成WAV格式的音频文件用于测试
"""

import struct
import math
import os

def create_simple_wav(filename, duration=3, sample_rate=16000, frequency=440):
    """
    创建简单的WAV文件
    
    Args:
        filename: 输出文件名
        duration: 持续时间（秒）
        sample_rate: 采样率
        frequency: 音频频率（Hz）
    """
    print(f"Creating WAV file: {filename}")
    
    # 创建audio目录
    audio_dir = "audio"
    if not os.path.exists(audio_dir):
        os.makedirs(audio_dir)
        print(f"Created directory: {audio_dir}")
    
    filepath = os.path.join(audio_dir, filename)
    
    # 计算参数
    num_samples = int(duration * sample_rate)
    data_size = num_samples * 2  # 16位 = 2字节
    file_size = data_size + 36
    
    with open(filepath, 'wb') as f:
        # RIFF头
        f.write(b'RIFF')
        f.write(struct.pack('<I', file_size))
        f.write(b'WAVE')
        
        # fmt块
        f.write(b'fmt ')
        f.write(struct.pack('<I', 16))  # fmt块大小
        f.write(struct.pack('<H', 1))   # PCM格式
        f.write(struct.pack('<H', 1))   # 单声道
        f.write(struct.pack('<I', sample_rate))  # 采样率
        f.write(struct.pack('<I', sample_rate * 2))  # 字节率
        f.write(struct.pack('<H', 2))   # 块对齐
        f.write(struct.pack('<H', 16))  # 位深度
        
        # data块
        f.write(b'data')
        f.write(struct.pack('<I', data_size))
        
        # 生成音频数据
        for i in range(num_samples):
            t = i / sample_rate
            amplitude = 0.3  # 音量
            sample = int(amplitude * 32767 * math.sin(2 * math.pi * frequency * t))
            f.write(struct.pack('<h', sample))
    
    file_size = os.path.getsize(filepath)
    print(f"Created: {filepath} ({file_size} bytes, {duration}s)")

def main():
    """创建测试音频文件"""
    print("=== Creating Simple Test Audio Files ===")
    
    # 创建几个不同的音频文件
    audio_files = [
        ("music.wav", 5, 440),      # 默认音乐 - A音符
        ("song1.wav", 4, 523),      # 歌曲1 - C音符
        ("song2.wav", 3, 659),      # 歌曲2 - E音符
        ("bgm.wav", 6, 349),        # 背景音乐 - F音符
        ("test.wav", 2, 880),       # 测试音频 - 高音A
    ]
    
    for filename, duration, frequency in audio_files:
        create_simple_wav(filename, duration, 16000, frequency)
    
    print("=== Audio Files Created Successfully ===")
    
    # 列出创建的文件
    audio_dir = "audio"
    if os.path.exists(audio_dir):
        print(f"\nFiles in {audio_dir}:")
        for file in sorted(os.listdir(audio_dir)):
            if file.endswith('.wav'):
                filepath = os.path.join(audio_dir, file)
                size = os.path.getsize(filepath)
                duration = size / (16000 * 2 + 44)  # 估算时长
                print(f"  {file} ({size} bytes)")
    
    print(f"\nServer will automatically load these files from the audio folder.")
    print(f"Use music control commands to play them:")
    print(f"  - start: Play current music")
    print(f"  - next: Switch to next music")
    print(f"  - prev: Switch to previous music")
    print(f"  - pause/stop: Stop playback")

if __name__ == "__main__":
    main()
