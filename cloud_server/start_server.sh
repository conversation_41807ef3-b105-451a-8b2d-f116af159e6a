#!/bin/bash

# SmartKid Cloud Server 启动脚本

echo "Starting SmartKid Cloud Server..."

# 检查Python版本
python_version=$(python3 --version 2>&1)
echo "Python version: $python_version"

# 检查并安装依赖
if [ -f "requirements.txt" ]; then
    echo "Installing dependencies..."
    pip3 install -r requirements.txt
fi

# 设置环境变量（可选）
export SMARTKID_HOST=${SMARTKID_HOST:-"0.0.0.0"}
export SMARTKID_PORT=${SMARTKID_PORT:-"8765"}
export LOG_LEVEL=${LOG_LEVEL:-"INFO"}

echo "Server configuration:"
echo "  Host: $SMARTKID_HOST"
echo "  Port: $SMARTKID_PORT"
echo "  Log Level: $LOG_LEVEL"

# 启动服务器
echo "Starting server..."
python3 smartkid_server.py
