#!/usr/bin/env python3
"""
SmartKid Cloud Server
支持与SmartKid终端的JSON协议通信
"""

import asyncio
import websockets
import json
import logging
import time
import uuid
from typing import Dict, Any, Optional, Set
from datetime import datetime
from config import config, TEST_COMMANDS, get_operation_description, is_valid_operation

# 配置日志
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format=config.LOG_FORMAT
)
logger = logging.getLogger('SmartKidServer')

class SmartKidServer:
    def __init__(self, host='localhost', port=8765):
        self.host = host
        self.port = port
        self.clients = {}  # 存储连接的客户端
        self.session_counter = 1
        
    async def register_client(self, websocket, path):
        """注册新客户端"""
        client_id = f"client_{len(self.clients) + 1}"
        self.clients[client_id] = {
            'websocket': websocket,
            'connected_at': datetime.now(),
            'last_activity': datetime.now()
        }
        logger.info(f"Client {client_id} connected from {websocket.remote_address}")
        return client_id
    
    async def unregister_client(self, client_id):
        """注销客户端"""
        if client_id in self.clients:
            del self.clients[client_id]
            logger.info(f"Client {client_id} disconnected")
    
    def generate_session_id(self) -> str:
        """生成会话ID"""
        return str(uuid.uuid4()).replace('-', '')
    
    def create_response(self, action: str, operate: str, text: str = "", 
                       code: int = 2200, message: str = "SUCCEED") -> Dict[str, Any]:
        """创建标准响应格式"""
        session_id = self.generate_session_id()
        response = {
            "code": code,
            "data": {
                "action": action,
                "operate": operate,
                "text": text
            },
            "message": message,
            "sessionId": session_id,
            "sn": self.session_counter
        }
        self.session_counter += 1
        return response
    
    async def handle_music_command(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理音乐命令"""
        operate = data.get('operate', '')
        body = data.get('body', '')
        
        logger.info(f"Processing music command: {operate}, body: {body}")
        
        if operate == 'start':
            return self.create_response('music', 'start', '开始播放音乐')
        elif operate == 'pause':
            return self.create_response('music', 'pause', '暂停音乐')
        elif operate == 'resume':
            return self.create_response('music', 'resume', '恢复音乐')
        elif operate == 'stop':
            return self.create_response('music', 'stop', '停止音乐')
        elif operate == 'next':
            return self.create_response('music', 'next', '下一首音乐')
        elif operate == 'prev':
            return self.create_response('music', 'prev', '上一首音乐')
        else:
            return self.create_response('music', operate, f'未知音乐操作: {operate}', 
                                      code=4000, message='ERROR')
    
    async def handle_story_command(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理故事命令"""
        operate = data.get('operate', '')
        body = data.get('body', '')
        
        logger.info(f"Processing story command: {operate}, body: {body}")
        
        if operate == 'start':
            return self.create_response('story', 'start', '开始播放故事')
        elif operate == 'pause':
            return self.create_response('story', 'pause', '暂停故事')
        elif operate == 'resume':
            return self.create_response('story', 'resume', '恢复故事')
        elif operate == 'stop':
            return self.create_response('story', 'stop', '停止故事')
        elif operate == 'next':
            return self.create_response('story', 'next', '下一个故事')
        elif operate == 'prev':
            return self.create_response('story', 'prev', '上一个故事')
        else:
            return self.create_response('story', operate, f'未知故事操作: {operate}', 
                                      code=4000, message='ERROR')
    
    async def handle_call_command(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理通话命令"""
        operate = data.get('operate', '')
        body = data.get('body', '')
        
        logger.info(f"Processing call command: {operate}, body: {body}")
        
        if operate == 'handshake':
            return self.create_response('call', 'handshake', '发起通话握手')
        elif operate == 'accept':
            return self.create_response('call', 'accept', '接受通话')
        elif operate == 'reject':
            return self.create_response('call', 'reject', '拒绝通话')
        elif operate == 'start':
            return self.create_response('call', 'start', '开始通话')
        elif operate == 'stop':
            return self.create_response('call', 'stop', '结束通话')
        else:
            return self.create_response('call', operate, f'未知通话操作: {operate}', 
                                      code=4000, message='ERROR')
    
    async def handle_cmd_command(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理命令控制"""
        operate = data.get('operate', '')
        body = data.get('body', '')
        
        logger.info(f"Processing cmd command: {operate}, body: {body}")
        
        cmd_responses = {
            'vol_up': '音量增加',
            'vol_down': '音量减少',
            'vol_max': '最大音量',
            'vol_min': '最小音量',
            'pause': '暂停',
            'resume': '恢复',
            'exit': '退出',
            'next': '下一个',
            'prev': '上一个',
            'start_music': '开始音乐',
            'stop_music': '停止音乐',
            'start_story': '开始故事',
            'stop_story': '停止故事',
            'start_call': '开始通话'
        }
        
        if operate in cmd_responses:
            return self.create_response('cmd', operate, cmd_responses[operate])
        else:
            return self.create_response('cmd', operate, f'未知命令操作: {operate}', 
                                      code=4000, message='ERROR')
    
    async def process_message(self, message: str) -> Optional[Dict[str, Any]]:
        """处理接收到的消息"""
        try:
            data = json.loads(message)
            logger.info(f"Received message: {data}")
            
            action = data.get('action', '')
            
            if action == 'music':
                return await self.handle_music_command(data)
            elif action == 'story':
                return await self.handle_story_command(data)
            elif action == 'call':
                return await self.handle_call_command(data)
            elif action == 'cmd':
                return await self.handle_cmd_command(data)
            elif action == 'test':
                return self.create_response('test', data.get('operate', 'test'), '测试响应')
            else:
                logger.warning(f"Unknown action: {action}")
                return self.create_response('error', 'unknown', f'未知动作: {action}', 
                                          code=4000, message='ERROR')
                
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {e}")
            return self.create_response('error', 'parse', 'JSON解析错误', 
                                      code=4000, message='ERROR')
        except Exception as e:
            logger.error(f"Process message error: {e}")
            return self.create_response('error', 'internal', '内部服务器错误', 
                                      code=5000, message='ERROR')
    
    async def send_periodic_commands(self, websocket, client_id):
        """定期发送测试命令"""
        await asyncio.sleep(5)  # 等待5秒后开始发送
        
        test_commands = [
            {'action': 'music', 'operate': 'start', 'text': '播放音乐'},
            {'action': 'story', 'operate': 'start', 'text': '讲故事'},
            {'action': 'cmd', 'operate': 'vol_up', 'text': '音量增加'},
            {'action': 'call', 'operate': 'handshake', 'text': '发起通话'},
        ]
        
        for i, cmd in enumerate(test_commands):
            try:
                if client_id not in self.clients:
                    break
                    
                response = self.create_response(cmd['action'], cmd['operate'], cmd['text'])
                await websocket.send(json.dumps(response, ensure_ascii=False))
                logger.info(f"Sent test command to {client_id}: {cmd['action']}.{cmd['operate']}")
                
                await asyncio.sleep(10)  # 每10秒发送一个命令
                
            except websockets.exceptions.ConnectionClosed:
                logger.info(f"Client {client_id} disconnected during periodic commands")
                break
            except Exception as e:
                logger.error(f"Error sending periodic command: {e}")
                break
    
    async def handle_client(self, websocket, path):
        """处理客户端连接"""
        client_id = await self.register_client(websocket, path)
        
        # 启动定期发送命令的任务
        periodic_task = asyncio.create_task(self.send_periodic_commands(websocket, client_id))
        
        try:
            async for message in websocket:
                # 更新最后活动时间
                if client_id in self.clients:
                    self.clients[client_id]['last_activity'] = datetime.now()
                
                # 处理消息
                response = await self.process_message(message)
                
                if response:
                    response_json = json.dumps(response, ensure_ascii=False)
                    await websocket.send(response_json)
                    logger.info(f"Sent response to {client_id}: {response}")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"Client {client_id} connection closed")
        except Exception as e:
            logger.error(f"Error handling client {client_id}: {e}")
        finally:
            periodic_task.cancel()
            await self.unregister_client(client_id)
    
    async def start_server(self):
        """启动服务器"""
        logger.info(f"Starting SmartKid server on {self.host}:{self.port}")
        
        async with websockets.serve(self.handle_client, self.host, self.port):
            logger.info(f"SmartKid server is running on ws://{self.host}:{self.port}")
            await asyncio.Future()  # 保持服务器运行

def main():
    """主函数"""
    server = SmartKidServer(host='************', port=8768)
    
    try:
        asyncio.run(server.start_server())
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")

if __name__ == "__main__":
    main()
