#!/usr/bin/env python3
"""
SmartKid Cloud Server
支持与SmartKid终端的JSON协议通信
"""

import asyncio
import websockets
import json
import logging
import time
import uuid
import os
import struct
import subprocess
from typing import Dict, Any, Optional, Set
from datetime import datetime
from config import config, TEST_COMMANDS, get_operation_description, is_valid_operation

# 配置日志
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format=config.LOG_FORMAT
)
logger = logging.getLogger('SmartKidServer')

class SmartKidServer:
    def __init__(self, host='localhost', port=8765):
        self.host = host
        self.port = port
        self.clients = {}  # 存储连接的客户端
        self.session_counter = 1
        self.audio_folder = "audio"  # 音频文件夹路径
        self.current_audio_tasks = {}  # 存储当前播放的音频任务
        
    async def register_client(self, websocket, path):
        """注册新客户端"""
        client_id = f"client_{len(self.clients) + 1}"
        self.clients[client_id] = {
            'websocket': websocket,
            'connected_at': datetime.now(),
            'last_activity': datetime.now()
        }
        logger.info(f"=== CLIENT REGISTRATION ===")
        logger.info(f"Client ID: {client_id}")
        logger.info(f"Remote address: {websocket.remote_address}")
        logger.info(f"Path: {path}")
        logger.info(f"Total clients: {len(self.clients)}")
        logger.info("=== END CLIENT REGISTRATION ===")
        return client_id

    async def unregister_client(self, client_id):
        """注销客户端"""
        if client_id in self.clients:
            logger.info(f"=== CLIENT UNREGISTRATION ===")
            logger.info(f"Client ID: {client_id}")
            logger.info(f"Connected at: {self.clients[client_id]['connected_at']}")
            logger.info(f"Last activity: {self.clients[client_id]['last_activity']}")

            # 清理音频任务
            if client_id in self.current_audio_tasks:
                self.current_audio_tasks[client_id].cancel()
                del self.current_audio_tasks[client_id]
                logger.info(f"Cancelled audio task for {client_id}")

            del self.clients[client_id]
            logger.info(f"Remaining clients: {len(self.clients)}")
            logger.info("=== END CLIENT UNREGISTRATION ===")
        else:
            logger.warning(f"Attempted to unregister unknown client: {client_id}")
    
    def generate_session_id(self) -> str:
        """生成会话ID"""
        return str(uuid.uuid4()).replace('-', '')
    
    def create_response(self, action: str, operate: str, text: str = "",
                       code: int = 2200, message: str = "SUCCEED") -> Dict[str, Any]:
        """创建标准响应格式"""
        session_id = self.generate_session_id()
        response = {
            "code": code,
            "data": {
                "action": action,
                "operate": operate,
                "text": text
            },
            "message": message,
            "sessionId": session_id,
            "sn": self.session_counter
        }
        self.session_counter += 1
        return response

    def get_audio_file_path(self, audio_name: str) -> Optional[str]:
        """获取音频文件路径"""
        # 支持的音频格式
        audio_extensions = ['.wav', '.mp3', '.opus', '.pcm']

        for ext in audio_extensions:
            file_path = os.path.join(self.audio_folder, f"{audio_name}{ext}")
            if os.path.exists(file_path):
                logger.info(f"Found audio file: {file_path}")
                return file_path

        # 如果没有找到，尝试默认音频文件
        default_files = ['music.wav', 'default.wav', 'test.wav']
        for default_file in default_files:
            file_path = os.path.join(self.audio_folder, default_file)
            if os.path.exists(file_path):
                logger.info(f"Using default audio file: {file_path}")
                return file_path

        logger.warning(f"No audio file found for: {audio_name}")
        return None

    async def send_opus_audio_stream(self, websocket, client_id: str, audio_name: str):
        """发送Opus编码的音频流"""
        try:
            audio_path = self.get_audio_file_path(audio_name)
            if not audio_path:
                logger.error(f"Audio file not found: {audio_name}")
                return

            logger.info(f"=== SENDING OPUS AUDIO STREAM TO {client_id} ===")
            logger.info(f"Audio file: {audio_path}")

            # 使用ffmpeg将音频文件转换为Opus编码流
            # 参数说明：
            # -i: 输入文件
            # -c:a libopus: 使用Opus编码器
            # -b:a 64k: 比特率64kbps
            # -ar 16000: 采样率16kHz
            # -ac 1: 单声道
            # -frame_duration 60: 帧长度60ms
            # -f opus: 输出格式为Opus
            # -: 输出到stdout

            ffmpeg_cmd = [
                'ffmpeg',
                '-i', audio_path,
                '-c:a', 'libopus',
                '-b:a', '64k',
                '-ar', '16000',
                '-ac', '1',
                '-frame_duration', '60',
                '-f', 'opus',
                '-'
            ]

            logger.info(f"Starting ffmpeg process: {' '.join(ffmpeg_cmd)}")

            # 启动ffmpeg进程
            process = subprocess.Popen(
                ffmpeg_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                bufsize=0
            )

            # 发送Opus流头信息（可选）
            opus_header = struct.pack('<II', 0x4F505553, 16000)  # 'OPUS' + 采样率
            await websocket.send(opus_header)
            logger.info(f"Sent Opus header: {len(opus_header)} bytes")

            # 读取并发送Opus编码数据
            chunk_size = 960  # Opus帧大小（60ms @ 16kHz）
            bytes_sent = 0
            frame_count = 0

            while True:
                # 读取Opus编码数据
                opus_data = process.stdout.read(chunk_size)
                if not opus_data:
                    break

                # 创建WebSocket二进制帧并发送
                await websocket.send(opus_data)
                bytes_sent += len(opus_data)
                frame_count += 1

                # 每100帧记录一次进度
                if frame_count % 100 == 0:
                    logger.info(f"Sent {frame_count} Opus frames, {bytes_sent} bytes")

                # 控制发送速度，模拟实时播放
                await asyncio.sleep(0.06)  # 60ms帧间隔

            # 等待ffmpeg进程结束
            process.wait()

            if process.returncode == 0:
                logger.info(f"Opus audio stream sent successfully: {frame_count} frames, {bytes_sent} bytes")
            else:
                stderr_output = process.stderr.read().decode('utf-8')
                logger.error(f"ffmpeg process failed with return code {process.returncode}")
                logger.error(f"ffmpeg stderr: {stderr_output}")

            logger.info("=== END SENDING OPUS AUDIO STREAM ===")

        except Exception as e:
            logger.error(f"Error sending Opus audio stream: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
    
    async def handle_music_command(self, data: Dict[str, Any], websocket, client_id: str) -> Dict[str, Any]:
        """处理音乐命令"""
        operate = data.get('operate', '')
        body = data.get('body', '')
        request_id = data.get('requestId', '')
        timestamp = data.get('timestamp', '')

        logger.info(f"=== HANDLING MUSIC COMMAND ===")
        logger.info(f"Full data: {data}")
        logger.info(f"Operate: '{operate}'")
        logger.info(f"Body: '{body}'")
        logger.info(f"RequestId: '{request_id}'")
        logger.info(f"Timestamp: '{timestamp}'")

        if operate == 'start':
            logger.info(f"Processing music start command")
            response = self.create_response('music', 'start', '开始播放音乐')
            logger.info(f"Created response for music start: {response}")

            # 启动Opus音频流发送任务
            audio_name = body if body and body != 'music' else 'music'
            logger.info(f"Starting Opus audio stream task for: {audio_name}")

            # 取消之前的音频任务（如果存在）
            if client_id in self.current_audio_tasks:
                self.current_audio_tasks[client_id].cancel()
                logger.info(f"Cancelled previous audio task for {client_id}")

            # 创建新的Opus音频流发送任务
            audio_task = asyncio.create_task(
                self.send_opus_audio_stream(websocket, client_id, audio_name)
            )
            self.current_audio_tasks[client_id] = audio_task

            return response
        elif operate == 'pause':
            logger.info(f"Processing music pause command")
            # 暂停音频发送任务
            if client_id in self.current_audio_tasks:
                self.current_audio_tasks[client_id].cancel()
                logger.info(f"Paused audio task for {client_id}")
            response = self.create_response('music', 'pause', '暂停音乐')
            logger.info(f"Created response for music pause: {response}")
            return response
        elif operate == 'resume':
            logger.info(f"Processing music resume command")
            # 恢复Opus音频流发送（重新开始）
            audio_name = body if body and body != 'music' else 'music'
            audio_task = asyncio.create_task(
                self.send_opus_audio_stream(websocket, client_id, audio_name)
            )
            self.current_audio_tasks[client_id] = audio_task
            response = self.create_response('music', 'resume', '恢复音乐')
            logger.info(f"Created response for music resume: {response}")
            return response
        elif operate == 'stop':
            logger.info(f"Processing music stop command")
            # 停止音频发送任务
            if client_id in self.current_audio_tasks:
                self.current_audio_tasks[client_id].cancel()
                del self.current_audio_tasks[client_id]
                logger.info(f"Stopped audio task for {client_id}")
            response = self.create_response('music', 'stop', '停止音乐')
            logger.info(f"Created response for music stop: {response}")
            return response
        elif operate == 'next':
            logger.info(f"Processing music next command")
            response = self.create_response('music', 'next', '下一首音乐')
            logger.info(f"Created response for music next: {response}")
            return response
        elif operate == 'prev':
            logger.info(f"Processing music prev command")
            response = self.create_response('music', 'prev', '上一首音乐')
            logger.info(f"Created response for music prev: {response}")
            return response
        else:
            logger.warning(f"Unknown music operate: '{operate}'")
            response = self.create_response('music', operate, f'未知音乐操作: {operate}',
                                      code=4000, message='ERROR')
            logger.info(f"Created error response for unknown music operate: {response}")
            return response
    
    async def handle_story_command(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理故事命令"""
        operate = data.get('operate', '')
        body = data.get('body', '')
        
        logger.info(f"Processing story command: {operate}, body: {body}")
        
        if operate == 'start':
            return self.create_response('story', 'start', '开始播放故事')
        elif operate == 'pause':
            return self.create_response('story', 'pause', '暂停故事')
        elif operate == 'resume':
            return self.create_response('story', 'resume', '恢复故事')
        elif operate == 'stop':
            return self.create_response('story', 'stop', '停止故事')
        elif operate == 'next':
            return self.create_response('story', 'next', '下一个故事')
        elif operate == 'prev':
            return self.create_response('story', 'prev', '上一个故事')
        else:
            return self.create_response('story', operate, f'未知故事操作: {operate}', 
                                      code=4000, message='ERROR')
    
    async def handle_call_command(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理通话命令"""
        operate = data.get('operate', '')
        body = data.get('body', '')
        
        logger.info(f"Processing call command: {operate}, body: {body}")
        
        if operate == 'handshake':
            return self.create_response('call', 'handshake', '发起通话握手')
        elif operate == 'accept':
            return self.create_response('call', 'accept', '接受通话')
        elif operate == 'reject':
            return self.create_response('call', 'reject', '拒绝通话')
        elif operate == 'start':
            return self.create_response('call', 'start', '开始通话')
        elif operate == 'stop':
            return self.create_response('call', 'stop', '结束通话')
        else:
            return self.create_response('call', operate, f'未知通话操作: {operate}', 
                                      code=4000, message='ERROR')
    
    async def handle_cmd_command(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理命令控制"""
        operate = data.get('operate', '')
        body = data.get('body', '')
        
        logger.info(f"Processing cmd command: {operate}, body: {body}")
        
        cmd_responses = {
            'vol_up': '音量增加',
            'vol_down': '音量减少',
            'vol_max': '最大音量',
            'vol_min': '最小音量',
            'pause': '暂停',
            'resume': '恢复',
            'exit': '退出',
            'next': '下一个',
            'prev': '上一个',
            'start_music': '开始音乐',
            'stop_music': '停止音乐',
            'start_story': '开始故事',
            'stop_story': '停止故事',
            'start_call': '开始通话'
        }
        
        if operate in cmd_responses:
            return self.create_response('cmd', operate, cmd_responses[operate])
        else:
            return self.create_response('cmd', operate, f'未知命令操作: {operate}', 
                                      code=4000, message='ERROR')
    
    async def process_message(self, message: str, websocket, client_id: str) -> Optional[Dict[str, Any]]:
        """处理接收到的消息"""
        try:
            logger.info(f"=== PROCESSING MESSAGE ===")
            logger.info(f"Raw message: {message}")

            data = json.loads(message)
            logger.info(f"Parsed JSON data: {data}")

            action = data.get('action', '')
            operate = data.get('operate', '')
            logger.info(f"Extracted action: '{action}', operate: '{operate}'")

            if action == 'music':
                logger.info(f"Routing to music handler")
                response = await self.handle_music_command(data, websocket, client_id)
                logger.info(f"Music handler returned: {response}")
                return response
            elif action == 'story':
                logger.info(f"Routing to story handler")
                response = await self.handle_story_command(data)
                logger.info(f"Story handler returned: {response}")
                return response
            elif action == 'call':
                logger.info(f"Routing to call handler")
                response = await self.handle_call_command(data)
                logger.info(f"Call handler returned: {response}")
                return response
            elif action == 'cmd':
                logger.info(f"Routing to cmd handler")
                response = await self.handle_cmd_command(data)
                logger.info(f"Cmd handler returned: {response}")
                return response
            elif action == 'test':
                logger.info(f"Routing to test handler")
                response = self.create_response('test', data.get('operate', 'test'), '测试响应')
                logger.info(f"Test handler returned: {response}")
                return response
            else:
                logger.warning(f"Unknown action: '{action}'")
                response = self.create_response('error', 'unknown', f'未知动作: {action}',
                                          code=4000, message='ERROR')
                logger.info(f"Error response for unknown action: {response}")
                return response

        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {e}")
            logger.error(f"Failed to parse message: {repr(message)}")
            response = self.create_response('error', 'parse', 'JSON解析错误',
                                      code=4000, message='ERROR')
            logger.info(f"JSON error response: {response}")
            return response
        except Exception as e:
            logger.error(f"Process message error: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            response = self.create_response('error', 'internal', '内部服务器错误',
                                      code=5000, message='ERROR')
            logger.info(f"Internal error response: {response}")
            return response
        finally:
            logger.info(f"=== END PROCESSING MESSAGE ===")
    
    async def send_periodic_commands(self, websocket, client_id):
        """定期发送测试命令"""
        await asyncio.sleep(5)  # 等待5秒后开始发送
        
        test_commands = [
            {'action': 'music', 'operate': 'start', 'text': '播放音乐'},
            {'action': 'story', 'operate': 'start', 'text': '讲故事'},
            {'action': 'cmd', 'operate': 'vol_up', 'text': '音量增加'},
            {'action': 'call', 'operate': 'handshake', 'text': '发起通话'},
        ]
        
        for i, cmd in enumerate(test_commands):
            try:
                if client_id not in self.clients:
                    break
                    
                response = self.create_response(cmd['action'], cmd['operate'], cmd['text'])
                await websocket.send(json.dumps(response, ensure_ascii=False))
                logger.info(f"Sent test command to {client_id}: {cmd['action']}.{cmd['operate']}")
                
                await asyncio.sleep(10)  # 每10秒发送一个命令
                
            except websockets.exceptions.ConnectionClosed:
                logger.info(f"Client {client_id} disconnected during periodic commands")
                break
            except Exception as e:
                logger.error(f"Error sending periodic command: {e}")
                break
    
    async def handle_client(self, websocket, path):
        """处理客户端连接"""
        client_id = await self.register_client(websocket, path)

        # 注释掉定期发送命令的任务（已禁用）
        # periodic_task = asyncio.create_task(self.send_periodic_commands(websocket, client_id))

        try:
            logger.info(f"=== CLIENT {client_id} READY TO RECEIVE MESSAGES ===")
            async for message in websocket:
                logger.info(f"=== RAW MESSAGE RECEIVED FROM {client_id} ===")
                logger.info(f"Message type: {type(message)}")
                logger.info(f"Message length: {len(message)}")
                logger.info(f"Message content (repr): {repr(message)}")
                logger.info(f"Message content (str): {message}")
                logger.info("=== END RAW MESSAGE ===")

                # 更新最后活动时间
                if client_id in self.clients:
                    self.clients[client_id]['last_activity'] = datetime.now()
                    logger.info(f"Updated last activity for {client_id}")

                # 处理消息
                logger.info(f"Starting to process message from {client_id}")
                response = await self.process_message(message, websocket, client_id)
                logger.info(f"Message processing completed for {client_id}")

                if response:
                    response_json = json.dumps(response, ensure_ascii=False)
                    logger.info(f"=== SENDING RESPONSE TO {client_id} ===")
                    logger.info(f"Response: {response_json}")
                    await websocket.send(response_json)
                    logger.info(f"Response sent successfully to {client_id}")
                    logger.info("=== END RESPONSE ===")
                else:
                    logger.warning(f"No response generated for message from {client_id}")

        except websockets.exceptions.ConnectionClosed:
            logger.info(f"Client {client_id} connection closed normally")
        except Exception as e:
            logger.error(f"Error handling client {client_id}: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
        finally:
            # periodic_task.cancel()  # 注释掉，因为没有启动这个任务
            await self.unregister_client(client_id)
            logger.info(f"Client {client_id} cleanup completed")
    
    async def start_server(self):
        """启动服务器"""
        logger.info(f"Starting SmartKid server on {self.host}:{self.port}")
        
        async with websockets.serve(self.handle_client, self.host, self.port):
            logger.info(f"SmartKid server is running on ws://{self.host}:{self.port}")
            await asyncio.Future()  # 保持服务器运行

def main():
    """主函数"""
    server = SmartKidServer(host='************', port=8768)
    
    try:
        asyncio.run(server.start_server())
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")

if __name__ == "__main__":
    main()
