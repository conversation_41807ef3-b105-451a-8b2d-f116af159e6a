#!/usr/bin/env python3
"""
SmartKid Cloud Server
支持与SmartKid终端的JSON协议通信
"""

import asyncio
import websockets
import json
import logging
import time
import uuid
import os
import struct
import wave
import opuslib
from typing import Dict, Any, Optional, Set
from datetime import datetime
from config import config, TEST_COMMANDS, get_operation_description, is_valid_operation

# 配置日志
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format=config.LOG_FORMAT
)
logger = logging.getLogger('SmartKidServer')

class SmartKidServer:
    def __init__(self, host='localhost', port=8765):
        self.host = host
        self.port = port
        self.clients = {}  # 存储连接的客户端
        self.session_counter = 1
        self.audio_folder = "audio"  # 音频文件夹路径
        self.current_audio_tasks = {}  # 存储当前播放的音频任务
        self.music_playlist = []  # 音乐播放列表
        self.current_music_index = {}  # 每个客户端当前播放的音乐索引
        
    async def register_client(self, websocket, path):
        """注册新客户端"""
        client_id = f"client_{len(self.clients) + 1}"
        self.clients[client_id] = {
            'websocket': websocket,
            'connected_at': datetime.now(),
            'last_activity': datetime.now()
        }
        logger.info(f"=== CLIENT REGISTRATION ===")
        logger.info(f"Client ID: {client_id}")
        logger.info(f"Remote address: {websocket.remote_address}")
        logger.info(f"Path: {path}")
        logger.info(f"Total clients: {len(self.clients)}")
        logger.info("=== END CLIENT REGISTRATION ===")
        return client_id

    async def unregister_client(self, client_id):
        """注销客户端"""
        if client_id in self.clients:
            logger.info(f"=== CLIENT UNREGISTRATION ===")
            logger.info(f"Client ID: {client_id}")
            logger.info(f"Connected at: {self.clients[client_id]['connected_at']}")
            logger.info(f"Last activity: {self.clients[client_id]['last_activity']}")

            # 清理音频任务
            if client_id in self.current_audio_tasks:
                self.current_audio_tasks[client_id].cancel()
                del self.current_audio_tasks[client_id]
                logger.info(f"Cancelled audio task for {client_id}")

            del self.clients[client_id]
            logger.info(f"Remaining clients: {len(self.clients)}")
            logger.info("=== END CLIENT UNREGISTRATION ===")
        else:
            logger.warning(f"Attempted to unregister unknown client: {client_id}")
    
    def generate_session_id(self) -> str:
        """生成会话ID"""
        return str(uuid.uuid4()).replace('-', '')

    def load_music_playlist(self):
        """加载音乐播放列表"""
        self.music_playlist = []

        if not os.path.exists(self.audio_folder):
            logger.warning(f"Audio folder not found: {self.audio_folder}")
            return

        # 扫描audio文件夹中的所有WAV文件
        for filename in os.listdir(self.audio_folder):
            if filename.lower().endswith('.wav'):
                file_path = os.path.join(self.audio_folder, filename)
                if os.path.isfile(file_path):
                    # 获取文件信息
                    file_size = os.path.getsize(file_path)
                    music_info = {
                        'name': os.path.splitext(filename)[0],
                        'filename': filename,
                        'path': file_path,
                        'size': file_size
                    }
                    self.music_playlist.append(music_info)

        # 按文件名排序
        self.music_playlist.sort(key=lambda x: x['name'])

        logger.info(f"Loaded music playlist with {len(self.music_playlist)} songs:")
        for i, music in enumerate(self.music_playlist):
            logger.info(f"  {i+1}. {music['name']} ({music['size']} bytes)")

    def get_current_music(self, client_id: str) -> Optional[Dict[str, Any]]:
        """获取当前应该播放的音乐"""
        if not self.music_playlist:
            self.load_music_playlist()

        if not self.music_playlist:
            logger.error("No music files found in playlist")
            return None

        # 如果客户端没有当前索引，从0开始
        if client_id not in self.current_music_index:
            self.current_music_index[client_id] = 0

        index = self.current_music_index[client_id]

        # 确保索引在有效范围内
        if index >= len(self.music_playlist):
            index = 0
            self.current_music_index[client_id] = 0

        current_music = self.music_playlist[index]
        logger.info(f"Current music for {client_id}: {current_music['name']} (index {index})")

        return current_music

    def next_music(self, client_id: str) -> Optional[Dict[str, Any]]:
        """切换到下一首音乐"""
        if not self.music_playlist:
            return None

        if client_id not in self.current_music_index:
            self.current_music_index[client_id] = 0

        # 移动到下一首
        self.current_music_index[client_id] = (self.current_music_index[client_id] + 1) % len(self.music_playlist)

        return self.get_current_music(client_id)

    def prev_music(self, client_id: str) -> Optional[Dict[str, Any]]:
        """切换到上一首音乐"""
        if not self.music_playlist:
            return None

        if client_id not in self.current_music_index:
            self.current_music_index[client_id] = 0

        # 移动到上一首
        self.current_music_index[client_id] = (self.current_music_index[client_id] - 1) % len(self.music_playlist)

        return self.get_current_music(client_id)
    
    def create_response(self, action: str, operate: str, text: str = "",
                       code: int = 2200, message: str = "SUCCEED") -> Dict[str, Any]:
        """创建标准响应格式"""
        session_id = self.generate_session_id()
        response = {
            "code": code,
            "data": {
                "action": action,
                "operate": operate,
                "text": text
            },
            "message": message,
            "sessionId": session_id,
            "sn": self.session_counter
        }
        self.session_counter += 1
        return response

    def parse_audio_name(self, body: str, data: Dict[str, Any]) -> str:
        """解析音频名称"""
        # 优先级1: 从body中解析
        if body and body.strip() and body.strip().lower() not in ['music', 'default', '']:
            audio_name = body.strip()
            logger.info(f"Using audio name from body: '{audio_name}'")
            return audio_name

        # 优先级2: 从data中的其他字段解析
        possible_fields = ['text', 'song', 'title', 'name', 'filename']
        for field in possible_fields:
            if field in data and data[field] and data[field].strip():
                audio_name = data[field].strip()
                if audio_name.lower() not in ['music', 'default', '']:
                    logger.info(f"Using audio name from {field}: '{audio_name}'")
                    return audio_name

        # 优先级3: 从requestId中解析（如果包含有意义的信息）
        request_id = data.get('requestId', '')
        if request_id and len(request_id) > 1 and not request_id.isdigit():
            logger.info(f"Using audio name from requestId: '{request_id}'")
            return request_id

        # 默认返回music
        logger.info("No specific audio name found, using default: 'music'")
        return 'music'

    def get_audio_file_path(self, audio_name: str) -> Optional[str]:
        """获取音频文件路径"""
        logger.info(f"Looking for audio file: '{audio_name}'")

        # 支持的音频格式（优先WAV格式）
        audio_extensions = ['.wav', '.mp3', '.opus', '.pcm']

        # 首先尝试精确匹配音频名称
        for ext in audio_extensions:
            file_path = os.path.join(self.audio_folder, f"{audio_name}{ext}")
            if os.path.exists(file_path):
                logger.info(f"Found exact match audio file: {file_path}")
                return file_path

        # 如果音频名称为空或者是通用名称，使用默认文件
        if not audio_name or audio_name.lower() in ['music', 'default', '']:
            default_files = ['music.wav', 'default.wav', 'test.wav']
            for default_file in default_files:
                file_path = os.path.join(self.audio_folder, default_file)
                if os.path.exists(file_path):
                    logger.info(f"Using default audio file: {file_path}")
                    return file_path

        # 尝试模糊匹配（不区分大小写）
        if os.path.exists(self.audio_folder):
            for filename in os.listdir(self.audio_folder):
                name_without_ext = os.path.splitext(filename)[0].lower()
                if audio_name.lower() in name_without_ext or name_without_ext in audio_name.lower():
                    file_path = os.path.join(self.audio_folder, filename)
                    logger.info(f"Found fuzzy match audio file: {file_path}")
                    return file_path

        # 最后尝试列出所有可用文件并使用第一个WAV文件
        if os.path.exists(self.audio_folder):
            wav_files = [f for f in os.listdir(self.audio_folder) if f.endswith('.wav')]
            if wav_files:
                file_path = os.path.join(self.audio_folder, wav_files[0])
                logger.info(f"Using first available WAV file: {file_path}")
                return file_path

        logger.warning(f"No audio file found for: '{audio_name}'")
        logger.info(f"Available files in {self.audio_folder}:")
        if os.path.exists(self.audio_folder):
            for filename in os.listdir(self.audio_folder):
                logger.info(f"  - {filename}")

        return None

    def read_wav_file(self, file_path: str) -> tuple:
        """读取WAV文件并返回音频数据和参数"""
        try:
            with wave.open(file_path, 'rb') as wav_file:
                # 获取WAV文件参数
                sample_rate = wav_file.getframerate()
                channels = wav_file.getnchannels()
                sample_width = wav_file.getsampwidth()
                frames = wav_file.getnframes()

                logger.info(f"WAV file info: {sample_rate}Hz, {channels}ch, {sample_width*8}bit, {frames} frames")

                # 读取所有音频数据
                audio_data = wav_file.readframes(frames)

                return audio_data, sample_rate, channels, sample_width

        except Exception as e:
            logger.error(f"Error reading WAV file {file_path}: {e}")
            return None, 0, 0, 0

    def convert_audio_format(self, audio_data: bytes, src_sample_rate: int, src_channels: int,
                           src_sample_width: int, target_sample_rate: int = 16000) -> bytes:
        """转换音频格式到目标格式"""
        try:
            import numpy as np

            # 将字节数据转换为numpy数组
            if src_sample_width == 1:
                dtype = np.uint8
                audio_array = np.frombuffer(audio_data, dtype=dtype).astype(np.float32) / 128.0 - 1.0
            elif src_sample_width == 2:
                dtype = np.int16
                audio_array = np.frombuffer(audio_data, dtype=dtype).astype(np.float32) / 32768.0
            elif src_sample_width == 4:
                dtype = np.int32
                audio_array = np.frombuffer(audio_data, dtype=dtype).astype(np.float32) / 2147483648.0
            else:
                raise ValueError(f"Unsupported sample width: {src_sample_width}")

            # 如果是立体声，转换为单声道
            if src_channels == 2:
                audio_array = audio_array.reshape(-1, 2).mean(axis=1)

            # 重采样到目标采样率（简单的线性插值）
            if src_sample_rate != target_sample_rate:
                from scipy import signal
                num_samples = int(len(audio_array) * target_sample_rate / src_sample_rate)
                audio_array = signal.resample(audio_array, num_samples)

            # 转换回16位PCM
            audio_array = np.clip(audio_array, -1.0, 1.0)
            pcm_data = (audio_array * 32767).astype(np.int16).tobytes()

            return pcm_data

        except ImportError:
            logger.warning("scipy not available, using simple format conversion")
            # 简单的格式转换（不进行重采样）
            if src_sample_width == 2 and src_channels == 1:
                return audio_data  # 已经是16位单声道
            else:
                # 简化处理：只支持16位转换
                if src_sample_width == 2 and src_channels == 2:
                    # 立体声转单声道
                    import struct
                    samples = struct.unpack(f'<{len(audio_data)//2}h', audio_data)
                    mono_samples = [int((samples[i] + samples[i+1]) / 2) for i in range(0, len(samples), 2)]
                    return struct.pack(f'<{len(mono_samples)}h', *mono_samples)
                else:
                    return audio_data
        except Exception as e:
            logger.error(f"Error converting audio format: {e}")
            return audio_data

    async def send_opus_audio_stream(self, websocket, client_id: str, audio_name: str):
        """使用opuslib发送Opus编码的音频流"""
        try:
            audio_path = self.get_audio_file_path(audio_name)
            if not audio_path:
                logger.error(f"Audio file not found: {audio_name}")
                return

            logger.info(f"=== SENDING OPUS AUDIO STREAM TO {client_id} ===")
            logger.info(f"Audio file: {audio_path}")

            # 读取WAV文件
            audio_data, sample_rate, channels, sample_width = self.read_wav_file(audio_path)
            if audio_data is None:
                logger.error(f"Failed to read audio file: {audio_path}")
                return

            # 转换音频格式到16kHz单声道16位PCM
            target_sample_rate = 16000
            pcm_data = self.convert_audio_format(audio_data, sample_rate, channels, sample_width, target_sample_rate)

            logger.info(f"Converted audio: {len(pcm_data)} bytes PCM data")

            # 创建Opus编码器
            # 参数：采样率, 声道数, 应用类型
            encoder = opuslib.Encoder(target_sample_rate, 1, opuslib.APPLICATION_AUDIO)
            encoder.bitrate = 64000  # 64kbps

            logger.info(f"Created Opus encoder: {target_sample_rate}Hz, 1ch, 64kbps")

            # 发送Opus流头信息
            opus_header = struct.pack('<II', 0x4F505553, target_sample_rate)  # 'OPUS' + 采样率
            await websocket.send(opus_header)
            logger.info(f"Sent Opus header: {len(opus_header)} bytes")

            # 计算帧参数
            frame_duration_ms = 60  # 60ms帧
            frame_size = int(target_sample_rate * frame_duration_ms / 1000)  # 每帧样本数
            frame_bytes = frame_size * 2  # 16位 = 2字节

            logger.info(f"Frame parameters: {frame_duration_ms}ms, {frame_size} samples, {frame_bytes} bytes")

            # 分帧编码并发送
            bytes_sent = 0
            frame_count = 0

            for i in range(0, len(pcm_data), frame_bytes):
                # 获取一帧PCM数据
                frame_pcm = pcm_data[i:i + frame_bytes]

                # 如果最后一帧不够长，用零填充
                if len(frame_pcm) < frame_bytes:
                    frame_pcm += b'\x00' * (frame_bytes - len(frame_pcm))

                # Opus编码
                opus_frame = encoder.encode(frame_pcm, frame_size)

                # 发送编码后的帧
                await websocket.send(opus_frame)
                bytes_sent += len(opus_frame)
                frame_count += 1

                # 每50帧记录一次进度
                if frame_count % 50 == 0:
                    logger.info(f"Sent {frame_count} Opus frames, {bytes_sent} bytes")

                # 控制发送速度，模拟实时播放
                await asyncio.sleep(frame_duration_ms / 1000.0)  # 60ms帧间隔

            logger.info(f"Opus audio stream sent successfully: {frame_count} frames, {bytes_sent} bytes")
            logger.info("=== END SENDING OPUS AUDIO STREAM ===")

        except Exception as e:
            logger.error(f"Error sending Opus audio stream: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

    async def send_opus_audio_stream_by_path(self, websocket, client_id: str, audio_path: str, music_name: str):
        """使用opuslib发送指定路径的Opus编码音频流"""
        try:
            if not os.path.exists(audio_path):
                logger.error(f"Audio file not found: {audio_path}")
                return

            logger.info(f"=== SENDING OPUS AUDIO STREAM TO {client_id} ===")
            logger.info(f"Music: {music_name}")
            logger.info(f"Audio file: {audio_path}")

            # 读取WAV文件
            audio_data, sample_rate, channels, sample_width = self.read_wav_file(audio_path)
            if audio_data is None:
                logger.error(f"Failed to read audio file: {audio_path}")
                return

            # 转换音频格式到16kHz单声道16位PCM
            target_sample_rate = 16000
            pcm_data = self.convert_audio_format(audio_data, sample_rate, channels, sample_width, target_sample_rate)

            logger.info(f"Converted audio: {len(pcm_data)} bytes PCM data")

            # 创建Opus编码器
            encoder = opuslib.Encoder(target_sample_rate, 1, opuslib.APPLICATION_AUDIO)
            encoder.bitrate = 64000  # 64kbps

            logger.info(f"Created Opus encoder: {target_sample_rate}Hz, 1ch, 64kbps")

            # 发送Opus流头信息
            opus_header = struct.pack('<II', 0x4F505553, target_sample_rate)  # 'OPUS' + 采样率
            await websocket.send(opus_header)
            logger.info(f"Sent Opus header: {len(opus_header)} bytes")

            # 计算帧参数
            frame_duration_ms = 60  # 60ms帧
            frame_size = int(target_sample_rate * frame_duration_ms / 1000)  # 每帧样本数
            frame_bytes = frame_size * 2  # 16位 = 2字节

            logger.info(f"Frame parameters: {frame_duration_ms}ms, {frame_size} samples, {frame_bytes} bytes")

            # 分帧编码并发送
            bytes_sent = 0
            frame_count = 0

            for i in range(0, len(pcm_data), frame_bytes):
                # 获取一帧PCM数据
                frame_pcm = pcm_data[i:i + frame_bytes]

                # 如果最后一帧不够长，用零填充
                if len(frame_pcm) < frame_bytes:
                    frame_pcm += b'\x00' * (frame_bytes - len(frame_pcm))

                # Opus编码
                opus_frame = encoder.encode(frame_pcm, frame_size)

                # 发送编码后的帧
                await websocket.send(opus_frame)
                bytes_sent += len(opus_frame)
                frame_count += 1

                # 每50帧记录一次进度
                if frame_count % 50 == 0:
                    logger.info(f"Sent {frame_count} Opus frames, {bytes_sent} bytes")

                # 控制发送速度，模拟实时播放
                await asyncio.sleep(frame_duration_ms / 1000.0)  # 60ms帧间隔

            logger.info(f"Opus audio stream sent successfully: {frame_count} frames, {bytes_sent} bytes")
            logger.info("=== END SENDING OPUS AUDIO STREAM ===")

        except Exception as e:
            logger.error(f"Error sending Opus audio stream: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
    
    async def handle_music_command(self, data: Dict[str, Any], websocket, client_id: str) -> Dict[str, Any]:
        """处理音乐命令"""
        operate = data.get('operate', '')
        body = data.get('body', '')
        request_id = data.get('requestId', '')
        timestamp = data.get('timestamp', '')

        logger.info(f"=== HANDLING MUSIC COMMAND ===")
        logger.info(f"Full data: {data}")
        logger.info(f"Operate: '{operate}'")
        logger.info(f"Body: '{body}'")
        logger.info(f"RequestId: '{request_id}'")
        logger.info(f"Timestamp: '{timestamp}'")

        if operate == 'start':
            logger.info(f"Processing music start command")

            # 获取当前应该播放的音乐
            current_music = self.get_current_music(client_id)
            if not current_music:
                logger.error("No music available to play")
                return self.create_response('music', 'start', '没有可播放的音乐文件',
                                          code=4004, message='ERROR')

            music_name = current_music['name']
            music_path = current_music['path']

            response = self.create_response('music', 'start', f'开始播放音乐: {music_name}')
            logger.info(f"Created response for music start: {response}")

            # 启动Opus音频流发送任务
            logger.info(f"Starting Opus audio stream for: '{music_name}' ({music_path})")

            # 取消之前的音频任务（如果存在）
            if client_id in self.current_audio_tasks:
                self.current_audio_tasks[client_id].cancel()
                logger.info(f"Cancelled previous audio task for {client_id}")

            # 创建新的Opus音频流发送任务
            audio_task = asyncio.create_task(
                self.send_opus_audio_stream_by_path(websocket, client_id, music_path, music_name)
            )
            self.current_audio_tasks[client_id] = audio_task

            return response
        elif operate == 'pause':
            logger.info(f"Processing music pause command")
            # 暂停音频发送任务
            if client_id in self.current_audio_tasks:
                self.current_audio_tasks[client_id].cancel()
                logger.info(f"Paused audio task for {client_id}")
            response = self.create_response('music', 'pause', '暂停音乐')
            logger.info(f"Created response for music pause: {response}")
            return response
        elif operate == 'resume':
            logger.info(f"Processing music resume command")

            # 恢复当前音乐的播放
            current_music = self.get_current_music(client_id)
            if not current_music:
                return self.create_response('music', 'resume', '没有可恢复的音乐',
                                          code=4004, message='ERROR')

            music_name = current_music['name']
            music_path = current_music['path']

            audio_task = asyncio.create_task(
                self.send_opus_audio_stream_by_path(websocket, client_id, music_path, music_name)
            )
            self.current_audio_tasks[client_id] = audio_task
            response = self.create_response('music', 'resume', f'恢复音乐: {music_name}')
            logger.info(f"Created response for music resume: {response}")
            return response
        elif operate == 'stop':
            logger.info(f"Processing music stop command")
            # 停止音频发送任务
            if client_id in self.current_audio_tasks:
                self.current_audio_tasks[client_id].cancel()
                del self.current_audio_tasks[client_id]
                logger.info(f"Stopped audio task for {client_id}")
            response = self.create_response('music', 'stop', '停止音乐')
            logger.info(f"Created response for music stop: {response}")
            return response
        elif operate == 'next':
            logger.info(f"Processing music next command")

            # 切换到下一首音乐
            next_music = self.next_music(client_id)
            if not next_music:
                return self.create_response('music', 'next', '没有下一首音乐',
                                          code=4004, message='ERROR')

            music_name = next_music['name']
            music_path = next_music['path']

            # 停止当前播放
            if client_id in self.current_audio_tasks:
                self.current_audio_tasks[client_id].cancel()
                logger.info(f"Stopped current music for next")

            # 开始播放下一首
            audio_task = asyncio.create_task(
                self.send_opus_audio_stream_by_path(websocket, client_id, music_path, music_name)
            )
            self.current_audio_tasks[client_id] = audio_task

            response = self.create_response('music', 'next', f'下一首音乐: {music_name}')
            logger.info(f"Created response for music next: {response}")
            return response

        elif operate == 'prev':
            logger.info(f"Processing music prev command")

            # 切换到上一首音乐
            prev_music = self.prev_music(client_id)
            if not prev_music:
                return self.create_response('music', 'prev', '没有上一首音乐',
                                          code=4004, message='ERROR')

            music_name = prev_music['name']
            music_path = prev_music['path']

            # 停止当前播放
            if client_id in self.current_audio_tasks:
                self.current_audio_tasks[client_id].cancel()
                logger.info(f"Stopped current music for prev")

            # 开始播放上一首
            audio_task = asyncio.create_task(
                self.send_opus_audio_stream_by_path(websocket, client_id, music_path, music_name)
            )
            self.current_audio_tasks[client_id] = audio_task

            response = self.create_response('music', 'prev', f'上一首音乐: {music_name}')
            logger.info(f"Created response for music prev: {response}")
            return response
        else:
            logger.warning(f"Unknown music operate: '{operate}'")
            response = self.create_response('music', operate, f'未知音乐操作: {operate}',
                                      code=4000, message='ERROR')
            logger.info(f"Created error response for unknown music operate: {response}")
            return response
    
    async def handle_story_command(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理故事命令"""
        operate = data.get('operate', '')
        body = data.get('body', '')
        
        logger.info(f"Processing story command: {operate}, body: {body}")
        
        if operate == 'start':
            return self.create_response('story', 'start', '开始播放故事')
        elif operate == 'pause':
            return self.create_response('story', 'pause', '暂停故事')
        elif operate == 'resume':
            return self.create_response('story', 'resume', '恢复故事')
        elif operate == 'stop':
            return self.create_response('story', 'stop', '停止故事')
        elif operate == 'next':
            return self.create_response('story', 'next', '下一个故事')
        elif operate == 'prev':
            return self.create_response('story', 'prev', '上一个故事')
        else:
            return self.create_response('story', operate, f'未知故事操作: {operate}', 
                                      code=4000, message='ERROR')
    
    async def handle_call_command(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理通话命令"""
        operate = data.get('operate', '')
        body = data.get('body', '')
        
        logger.info(f"Processing call command: {operate}, body: {body}")
        
        if operate == 'handshake':
            return self.create_response('call', 'handshake', '发起通话握手')
        elif operate == 'accept':
            return self.create_response('call', 'accept', '接受通话')
        elif operate == 'reject':
            return self.create_response('call', 'reject', '拒绝通话')
        elif operate == 'start':
            return self.create_response('call', 'start', '开始通话')
        elif operate == 'stop':
            return self.create_response('call', 'stop', '结束通话')
        else:
            return self.create_response('call', operate, f'未知通话操作: {operate}', 
                                      code=4000, message='ERROR')
    
    async def handle_cmd_command(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理命令控制"""
        operate = data.get('operate', '')
        body = data.get('body', '')
        
        logger.info(f"Processing cmd command: {operate}, body: {body}")
        
        cmd_responses = {
            'vol_up': '音量增加',
            'vol_down': '音量减少',
            'vol_max': '最大音量',
            'vol_min': '最小音量',
            'pause': '暂停',
            'resume': '恢复',
            'exit': '退出',
            'next': '下一个',
            'prev': '上一个',
            'start_music': '开始音乐',
            'stop_music': '停止音乐',
            'start_story': '开始故事',
            'stop_story': '停止故事',
            'start_call': '开始通话'
        }
        
        if operate in cmd_responses:
            return self.create_response('cmd', operate, cmd_responses[operate])
        else:
            return self.create_response('cmd', operate, f'未知命令操作: {operate}', 
                                      code=4000, message='ERROR')
    
    async def process_message(self, message: str, websocket, client_id: str) -> Optional[Dict[str, Any]]:
        """处理接收到的消息"""
        try:
            logger.info(f"=== PROCESSING MESSAGE ===")
            logger.info(f"Raw message: {message}")

            data = json.loads(message)
            logger.info(f"Parsed JSON data: {data}")

            action = data.get('action', '')
            operate = data.get('operate', '')
            logger.info(f"Extracted action: '{action}', operate: '{operate}'")

            if action == 'music':
                logger.info(f"Routing to music handler")
                response = await self.handle_music_command(data, websocket, client_id)
                logger.info(f"Music handler returned: {response}")
                return response
            elif action == 'story':
                logger.info(f"Routing to story handler")
                response = await self.handle_story_command(data)
                logger.info(f"Story handler returned: {response}")
                return response
            elif action == 'call':
                logger.info(f"Routing to call handler")
                response = await self.handle_call_command(data)
                logger.info(f"Call handler returned: {response}")
                return response
            elif action == 'cmd':
                logger.info(f"Routing to cmd handler")
                response = await self.handle_cmd_command(data)
                logger.info(f"Cmd handler returned: {response}")
                return response
            elif action == 'test':
                logger.info(f"Routing to test handler")
                response = self.create_response('test', data.get('operate', 'test'), '测试响应')
                logger.info(f"Test handler returned: {response}")
                return response
            else:
                logger.warning(f"Unknown action: '{action}'")
                response = self.create_response('error', 'unknown', f'未知动作: {action}',
                                          code=4000, message='ERROR')
                logger.info(f"Error response for unknown action: {response}")
                return response

        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {e}")
            logger.error(f"Failed to parse message: {repr(message)}")
            response = self.create_response('error', 'parse', 'JSON解析错误',
                                      code=4000, message='ERROR')
            logger.info(f"JSON error response: {response}")
            return response
        except Exception as e:
            logger.error(f"Process message error: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            response = self.create_response('error', 'internal', '内部服务器错误',
                                      code=5000, message='ERROR')
            logger.info(f"Internal error response: {response}")
            return response
        finally:
            logger.info(f"=== END PROCESSING MESSAGE ===")
    
    async def send_periodic_commands(self, websocket, client_id):
        """定期发送测试命令"""
        await asyncio.sleep(5)  # 等待5秒后开始发送
        
        test_commands = [
            {'action': 'music', 'operate': 'start', 'text': '播放音乐'},
            {'action': 'story', 'operate': 'start', 'text': '讲故事'},
            {'action': 'cmd', 'operate': 'vol_up', 'text': '音量增加'},
            {'action': 'call', 'operate': 'handshake', 'text': '发起通话'},
        ]
        
        for i, cmd in enumerate(test_commands):
            try:
                if client_id not in self.clients:
                    break
                    
                response = self.create_response(cmd['action'], cmd['operate'], cmd['text'])
                await websocket.send(json.dumps(response, ensure_ascii=False))
                logger.info(f"Sent test command to {client_id}: {cmd['action']}.{cmd['operate']}")
                
                await asyncio.sleep(10)  # 每10秒发送一个命令
                
            except websockets.exceptions.ConnectionClosed:
                logger.info(f"Client {client_id} disconnected during periodic commands")
                break
            except Exception as e:
                logger.error(f"Error sending periodic command: {e}")
                break
    
    async def handle_client(self, websocket, path):
        """处理客户端连接"""
        client_id = await self.register_client(websocket, path)

        # 注释掉定期发送命令的任务（已禁用）
        # periodic_task = asyncio.create_task(self.send_periodic_commands(websocket, client_id))

        try:
            logger.info(f"=== CLIENT {client_id} READY TO RECEIVE MESSAGES ===")
            async for message in websocket:
                logger.info(f"=== RAW MESSAGE RECEIVED FROM {client_id} ===")
                logger.info(f"Message type: {type(message)}")
                logger.info(f"Message length: {len(message)}")
                logger.info(f"Message content (repr): {repr(message)}")
                logger.info(f"Message content (str): {message}")
                logger.info("=== END RAW MESSAGE ===")

                # 更新最后活动时间
                if client_id in self.clients:
                    self.clients[client_id]['last_activity'] = datetime.now()
                    logger.info(f"Updated last activity for {client_id}")

                # 处理消息
                logger.info(f"Starting to process message from {client_id}")
                response = await self.process_message(message, websocket, client_id)
                logger.info(f"Message processing completed for {client_id}")

                if response:
                    response_json = json.dumps(response, ensure_ascii=False)
                    logger.info(f"=== SENDING RESPONSE TO {client_id} ===")
                    logger.info(f"Response: {response_json}")
                    await websocket.send(response_json)
                    logger.info(f"Response sent successfully to {client_id}")
                    logger.info("=== END RESPONSE ===")
                else:
                    logger.warning(f"No response generated for message from {client_id}")

        except websockets.exceptions.ConnectionClosed:
            logger.info(f"Client {client_id} connection closed normally")
        except Exception as e:
            logger.error(f"Error handling client {client_id}: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
        finally:
            # periodic_task.cancel()  # 注释掉，因为没有启动这个任务
            await self.unregister_client(client_id)
            logger.info(f"Client {client_id} cleanup completed")
    
    async def start_server(self):
        """启动服务器"""
        logger.info(f"Starting SmartKid server on {self.host}:{self.port}")

        # 加载音乐播放列表
        self.load_music_playlist()

        async with websockets.serve(self.handle_client, self.host, self.port):
            logger.info(f"SmartKid server is running on ws://{self.host}:{self.port}")
            await asyncio.Future()  # 保持服务器运行

def main():
    """主函数"""
    server = SmartKidServer(host='************', port=8768)
    
    try:
        asyncio.run(server.start_server())
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")

if __name__ == "__main__":
    main()
