<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartKid Cloud Server - Web Interface</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .status {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .status-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            flex: 1;
            margin: 0 10px;
        }
        .status-item.connected {
            background: #d4edda;
            color: #155724;
        }
        .status-item.disconnected {
            background: #f8d7da;
            color: #721c24;
        }
        .controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        .control-panel {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .control-panel h3 {
            margin-top: 0;
            color: #495057;
        }
        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .btn:hover { opacity: 0.8; }
        .logs {
            background: #212529;
            color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .input-group {
            display: flex;
            margin-bottom: 15px;
        }
        .input-group input, .input-group select {
            flex: 1;
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            margin-right: 10px;
        }
        .connection-info {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 SmartKid Cloud Server</h1>
            <p>Web管理界面 - JSON协议测试工具</p>
        </div>

        <div class="connection-info">
            <strong>服务器地址:</strong> <span id="serverUrl">ws://localhost:8765</span>
            <button class="btn btn-primary" onclick="connect()" id="connectBtn">连接</button>
            <button class="btn btn-danger" onclick="disconnect()" id="disconnectBtn" disabled>断开</button>
        </div>

        <div class="status">
            <div class="status-item" id="connectionStatus">
                <h4>连接状态</h4>
                <p id="statusText">未连接</p>
            </div>
            <div class="status-item">
                <h4>发送消息数</h4>
                <p id="sentCount">0</p>
            </div>
            <div class="status-item">
                <h4>接收消息数</h4>
                <p id="receivedCount">0</p>
            </div>
            <div class="status-item">
                <h4>连接时长</h4>
                <p id="connectionTime">00:00:00</p>
            </div>
        </div>

        <div class="controls">
            <div class="control-panel">
                <h3>🎵 音乐控制</h3>
                <div class="button-group">
                    <button class="btn btn-success" onclick="sendCommand('music', 'start')">开始</button>
                    <button class="btn btn-warning" onclick="sendCommand('music', 'pause')">暂停</button>
                    <button class="btn btn-info" onclick="sendCommand('music', 'resume')">恢复</button>
                    <button class="btn btn-danger" onclick="sendCommand('music', 'stop')">停止</button>
                    <button class="btn btn-primary" onclick="sendCommand('music', 'next')">下一首</button>
                    <button class="btn btn-primary" onclick="sendCommand('music', 'prev')">上一首</button>
                </div>
            </div>

            <div class="control-panel">
                <h3>📚 故事播放</h3>
                <div class="button-group">
                    <button class="btn btn-success" onclick="sendCommand('story', 'start')">开始</button>
                    <button class="btn btn-warning" onclick="sendCommand('story', 'pause')">暂停</button>
                    <button class="btn btn-info" onclick="sendCommand('story', 'resume')">恢复</button>
                    <button class="btn btn-danger" onclick="sendCommand('story', 'stop')">停止</button>
                    <button class="btn btn-primary" onclick="sendCommand('story', 'next')">下一个</button>
                    <button class="btn btn-primary" onclick="sendCommand('story', 'prev')">上一个</button>
                </div>
            </div>

            <div class="control-panel">
                <h3>📞 通话控制</h3>
                <div class="button-group">
                    <button class="btn btn-info" onclick="sendCommand('call', 'handshake')">握手</button>
                    <button class="btn btn-success" onclick="sendCommand('call', 'accept')">接受</button>
                    <button class="btn btn-warning" onclick="sendCommand('call', 'reject')">拒绝</button>
                    <button class="btn btn-primary" onclick="sendCommand('call', 'start')">开始</button>
                    <button class="btn btn-danger" onclick="sendCommand('call', 'stop')">结束</button>
                </div>
            </div>

            <div class="control-panel">
                <h3>🎛️ 命令控制</h3>
                <div class="button-group">
                    <button class="btn btn-info" onclick="sendCommand('cmd', 'vol_up')">音量+</button>
                    <button class="btn btn-info" onclick="sendCommand('cmd', 'vol_down')">音量-</button>
                    <button class="btn btn-warning" onclick="sendCommand('cmd', 'pause')">暂停</button>
                    <button class="btn btn-success" onclick="sendCommand('cmd', 'resume')">恢复</button>
                    <button class="btn btn-primary" onclick="sendCommand('cmd', 'next')">下一个</button>
                    <button class="btn btn-primary" onclick="sendCommand('cmd', 'prev')">上一个</button>
                </div>
            </div>
        </div>

        <div class="control-panel">
            <h3>🛠️ 自定义命令</h3>
            <div class="input-group">
                <select id="customAction">
                    <option value="music">music</option>
                    <option value="story">story</option>
                    <option value="call">call</option>
                    <option value="cmd">cmd</option>
                    <option value="test">test</option>
                </select>
                <input type="text" id="customOperate" placeholder="operate (如: start, pause)" />
                <input type="text" id="customBody" placeholder="body (消息内容)" />
                <button class="btn btn-primary" onclick="sendCustomCommand()">发送</button>
            </div>
        </div>

        <div class="control-panel">
            <h3>📋 通信日志</h3>
            <div class="logs" id="logs"></div>
            <button class="btn btn-warning" onclick="clearLogs()">清除日志</button>
        </div>
    </div>

    <script>
        let websocket = null;
        let requestId = 1;
        let sentCount = 0;
        let receivedCount = 0;
        let connectTime = null;
        let timeInterval = null;

        function connect() {
            const url = document.getElementById('serverUrl').textContent;
            websocket = new WebSocket(url);

            websocket.onopen = function(event) {
                updateConnectionStatus(true);
                addLog('✅ 连接成功', 'success');
                connectTime = new Date();
                startTimer();
            };

            websocket.onmessage = function(event) {
                receivedCount++;
                document.getElementById('receivedCount').textContent = receivedCount;
                
                try {
                    const data = JSON.parse(event.data);
                    addLog(`📥 接收: ${JSON.stringify(data, null, 2)}`, 'receive');
                } catch (e) {
                    addLog(`📥 接收: ${event.data}`, 'receive');
                }
            };

            websocket.onclose = function(event) {
                updateConnectionStatus(false);
                addLog('❌ 连接关闭', 'error');
                stopTimer();
            };

            websocket.onerror = function(error) {
                addLog(`❌ 连接错误: ${error}`, 'error');
            };
        }

        function disconnect() {
            if (websocket) {
                websocket.close();
                websocket = null;
            }
        }

        function updateConnectionStatus(connected) {
            const statusElement = document.getElementById('connectionStatus');
            const statusText = document.getElementById('statusText');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');

            if (connected) {
                statusElement.className = 'status-item connected';
                statusText.textContent = '已连接';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
            } else {
                statusElement.className = 'status-item disconnected';
                statusText.textContent = '未连接';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            }
        }

        function sendCommand(action, operate, body = '') {
            if (!websocket || websocket.readyState !== WebSocket.OPEN) {
                addLog('❌ 未连接到服务器', 'error');
                return;
            }

            const message = {
                action: action,
                operate: operate,
                requestId: requestId.toString(),
                timestamp: Date.now(),
                body: body || `${action} ${operate} command`
            };

            websocket.send(JSON.stringify(message));
            sentCount++;
            document.getElementById('sentCount').textContent = sentCount;
            requestId++;

            addLog(`📤 发送: ${JSON.stringify(message, null, 2)}`, 'send');
        }

        function sendCustomCommand() {
            const action = document.getElementById('customAction').value;
            const operate = document.getElementById('customOperate').value;
            const body = document.getElementById('customBody').value;

            if (!operate) {
                addLog('❌ 请输入operate参数', 'error');
                return;
            }

            sendCommand(action, operate, body);
        }

        function addLog(message, type = 'info') {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            
            logs.textContent += logEntry;
            logs.scrollTop = logs.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('logs').textContent = '';
        }

        function startTimer() {
            timeInterval = setInterval(() => {
                if (connectTime) {
                    const now = new Date();
                    const diff = now - connectTime;
                    const hours = Math.floor(diff / 3600000);
                    const minutes = Math.floor((diff % 3600000) / 60000);
                    const seconds = Math.floor((diff % 60000) / 1000);
                    
                    document.getElementById('connectionTime').textContent = 
                        `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                }
            }, 1000);
        }

        function stopTimer() {
            if (timeInterval) {
                clearInterval(timeInterval);
                timeInterval = null;
            }
            document.getElementById('connectionTime').textContent = '00:00:00';
        }

        // 页面加载完成后自动连接
        window.onload = function() {
            addLog('🚀 Web界面已加载', 'info');
        };
    </script>
</body>
</html>
