#!/usr/bin/env python3
"""
SmartKid Cloud Server Configuration
"""

import os
from typing import Dict, Any

class ServerConfig:
    """服务器配置类"""
    
    def __init__(self):
        # 服务器基本配置
        self.HOST = os.getenv('SMARTKID_HOST', '0.0.0.0')
        self.PORT = int(os.getenv('SMARTKID_PORT', '8765'))
        
        # 日志配置
        self.LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
        self.LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        # WebSocket配置
        self.WS_PING_INTERVAL = 20  # WebSocket ping间隔（秒）
        self.WS_PING_TIMEOUT = 10   # WebSocket ping超时（秒）
        self.WS_CLOSE_TIMEOUT = 10  # WebSocket关闭超时（秒）
        
        # 客户端管理配置
        self.CLIENT_TIMEOUT = 300   # 客户端超时时间（秒）
        self.MAX_CLIENTS = 100      # 最大客户端连接数
        
        # 消息配置
        self.MAX_MESSAGE_SIZE = 1024 * 10  # 最大消息大小（字节）
        self.MESSAGE_QUEUE_SIZE = 100       # 消息队列大小
        
        # 测试命令配置
        self.ENABLE_PERIODIC_COMMANDS = True  # 是否启用定期发送测试命令
        self.PERIODIC_COMMAND_INTERVAL = 10   # 定期命令发送间隔（秒）
        self.PERIODIC_COMMAND_DELAY = 5       # 首次发送延迟（秒）
        
        # 响应配置
        self.DEFAULT_SUCCESS_CODE = 2200
        self.DEFAULT_SUCCESS_MESSAGE = "SUCCEED"
        self.DEFAULT_ERROR_CODE = 4000
        self.DEFAULT_ERROR_MESSAGE = "ERROR"
        self.DEFAULT_SERVER_ERROR_CODE = 5000
        self.DEFAULT_SERVER_ERROR_MESSAGE = "SERVER_ERROR"
    
    def get_websocket_config(self) -> Dict[str, Any]:
        """获取WebSocket配置"""
        return {
            'ping_interval': self.WS_PING_INTERVAL,
            'ping_timeout': self.WS_PING_TIMEOUT,
            'close_timeout': self.WS_CLOSE_TIMEOUT,
            'max_size': self.MAX_MESSAGE_SIZE,
            'max_queue': self.MESSAGE_QUEUE_SIZE
        }
    
    def get_server_info(self) -> Dict[str, Any]:
        """获取服务器信息"""
        return {
            'host': self.HOST,
            'port': self.PORT,
            'max_clients': self.MAX_CLIENTS,
            'client_timeout': self.CLIENT_TIMEOUT,
            'log_level': self.LOG_LEVEL
        }

# 全局配置实例
config = ServerConfig()

# 预定义的测试命令
TEST_COMMANDS = [
    {
        'action': 'music',
        'operate': 'start',
        'text': '播放音乐',
        'description': '开始播放音乐'
    },
    {
        'action': 'story',
        'operate': 'start',
        'text': '讲故事',
        'description': '开始讲故事'
    },
    {
        'action': 'cmd',
        'operate': 'vol_up',
        'text': '音量增加',
        'description': '增加音量'
    },
    {
        'action': 'call',
        'operate': 'handshake',
        'text': '发起通话',
        'description': '发起通话握手'
    },
    {
        'action': 'cmd',
        'operate': 'pause',
        'text': '暂停',
        'description': '暂停当前操作'
    },
    {
        'action': 'music',
        'operate': 'next',
        'text': '下一首',
        'description': '播放下一首音乐'
    },
    {
        'action': 'story',
        'operate': 'next',
        'text': '下一个故事',
        'description': '播放下一个故事'
    },
    {
        'action': 'cmd',
        'operate': 'start_call',
        'text': '开始通话',
        'description': '通过命令开始通话'
    }
]

# 支持的操作映射
SUPPORTED_OPERATIONS = {
    'music': ['start', 'pause', 'resume', 'stop', 'next', 'prev'],
    'story': ['start', 'pause', 'resume', 'stop', 'next', 'prev'],
    'call': ['handshake', 'accept', 'reject', 'start', 'stop'],
    'cmd': [
        'vol_up', 'vol_down', 'vol_max', 'vol_min',
        'pause', 'resume', 'exit', 'next', 'prev',
        'start_music', 'stop_music', 'start_story', 'stop_story', 'start_call'
    ],
    'test': ['ping', 'echo', 'status']
}

# 操作描述映射
OPERATION_DESCRIPTIONS = {
    'music': {
        'start': '开始播放音乐',
        'pause': '暂停音乐',
        'resume': '恢复音乐',
        'stop': '停止音乐',
        'next': '下一首音乐',
        'prev': '上一首音乐'
    },
    'story': {
        'start': '开始播放故事',
        'pause': '暂停故事',
        'resume': '恢复故事',
        'stop': '停止故事',
        'next': '下一个故事',
        'prev': '上一个故事'
    },
    'call': {
        'handshake': '发起通话握手',
        'accept': '接受通话',
        'reject': '拒绝通话',
        'start': '开始通话',
        'stop': '结束通话'
    },
    'cmd': {
        'vol_up': '音量增加',
        'vol_down': '音量减少',
        'vol_max': '最大音量',
        'vol_min': '最小音量',
        'pause': '暂停',
        'resume': '恢复',
        'exit': '退出',
        'next': '下一个',
        'prev': '上一个',
        'start_music': '开始音乐',
        'stop_music': '停止音乐',
        'start_story': '开始故事',
        'stop_story': '停止故事',
        'start_call': '开始通话'
    },
    'test': {
        'ping': '测试连接',
        'echo': '回声测试',
        'status': '状态查询'
    }
}

def get_operation_description(action: str, operate: str) -> str:
    """获取操作描述"""
    return OPERATION_DESCRIPTIONS.get(action, {}).get(operate, f'{action}.{operate}')

def is_valid_operation(action: str, operate: str) -> bool:
    """检查操作是否有效"""
    return action in SUPPORTED_OPERATIONS and operate in SUPPORTED_OPERATIONS[action]
