#!/usr/bin/env python3
"""
创建测试音频文件
生成简单的PCM音频数据用于测试
"""

import struct
import math
import os

def create_test_audio_pcm(filename, duration=5, sample_rate=16000, frequency=440):
    """
    创建测试音频文件（PCM格式）
    
    Args:
        filename: 输出文件名
        duration: 持续时间（秒）
        sample_rate: 采样率
        frequency: 音频频率（Hz）
    """
    print(f"Creating test audio: {filename}")
    print(f"Duration: {duration}s, Sample rate: {sample_rate}Hz, Frequency: {frequency}Hz")
    
    # 创建audio目录
    audio_dir = "audio"
    if not os.path.exists(audio_dir):
        os.makedirs(audio_dir)
        print(f"Created directory: {audio_dir}")
    
    filepath = os.path.join(audio_dir, filename)
    
    with open(filepath, 'wb') as f:
        for i in range(int(duration * sample_rate)):
            # 生成正弦波
            t = i / sample_rate
            amplitude = 0.3  # 音量（0-1）
            sample = int(amplitude * 32767 * math.sin(2 * math.pi * frequency * t))
            
            # 写入16位PCM数据（小端序）
            f.write(struct.pack('<h', sample))
    
    file_size = os.path.getsize(filepath)
    print(f"Created audio file: {filepath} ({file_size} bytes)")

def create_test_wav(filename, duration=5, sample_rate=16000, frequency=440):
    """
    创建测试WAV文件
    """
    print(f"Creating test WAV: {filename}")
    
    # 创建audio目录
    audio_dir = "audio"
    if not os.path.exists(audio_dir):
        os.makedirs(audio_dir)
    
    filepath = os.path.join(audio_dir, filename)
    
    # WAV文件头
    num_samples = int(duration * sample_rate)
    data_size = num_samples * 2  # 16位 = 2字节
    file_size = data_size + 36
    
    with open(filepath, 'wb') as f:
        # RIFF头
        f.write(b'RIFF')
        f.write(struct.pack('<I', file_size))
        f.write(b'WAVE')
        
        # fmt块
        f.write(b'fmt ')
        f.write(struct.pack('<I', 16))  # fmt块大小
        f.write(struct.pack('<H', 1))   # PCM格式
        f.write(struct.pack('<H', 1))   # 单声道
        f.write(struct.pack('<I', sample_rate))  # 采样率
        f.write(struct.pack('<I', sample_rate * 2))  # 字节率
        f.write(struct.pack('<H', 2))   # 块对齐
        f.write(struct.pack('<H', 16))  # 位深度
        
        # data块
        f.write(b'data')
        f.write(struct.pack('<I', data_size))
        
        # 音频数据
        for i in range(num_samples):
            t = i / sample_rate
            amplitude = 0.3
            sample = int(amplitude * 32767 * math.sin(2 * math.pi * frequency * t))
            f.write(struct.pack('<h', sample))
    
    file_size = os.path.getsize(filepath)
    print(f"Created WAV file: {filepath} ({file_size} bytes)")

def create_test_music_wav():
    """创建一个简单的音乐测试文件（多音符）"""
    print("Creating test music with multiple notes...")

    audio_dir = "audio"
    if not os.path.exists(audio_dir):
        os.makedirs(audio_dir)

    filepath = os.path.join(audio_dir, "music.wav")
    sample_rate = 16000
    duration_per_note = 1.0  # 每个音符1秒

    # 简单的音符序列 (C-D-E-F-G)
    frequencies = [261, 294, 329, 349, 392]  # C4, D4, E4, F4, G4

    total_samples = int(len(frequencies) * duration_per_note * sample_rate)
    data_size = total_samples * 2
    file_size = data_size + 36

    with open(filepath, 'wb') as f:
        # WAV文件头
        f.write(b'RIFF')
        f.write(struct.pack('<I', file_size))
        f.write(b'WAVE')

        # fmt块
        f.write(b'fmt ')
        f.write(struct.pack('<I', 16))
        f.write(struct.pack('<H', 1))   # PCM
        f.write(struct.pack('<H', 1))   # 单声道
        f.write(struct.pack('<I', sample_rate))
        f.write(struct.pack('<I', sample_rate * 2))
        f.write(struct.pack('<H', 2))
        f.write(struct.pack('<H', 16))

        # data块
        f.write(b'data')
        f.write(struct.pack('<I', data_size))

        # 生成音符序列
        for freq in frequencies:
            samples_per_note = int(duration_per_note * sample_rate)
            for i in range(samples_per_note):
                t = i / sample_rate
                amplitude = 0.3 * (1.0 - t / duration_per_note)  # 渐弱效果
                sample = int(amplitude * 32767 * math.sin(2 * math.pi * freq * t))
                f.write(struct.pack('<h', sample))

    file_size = os.path.getsize(filepath)
    print(f"Created music.wav: {filepath} ({file_size} bytes)")

def main():
    """创建测试音频文件"""
    print("=== Creating Test Audio Files for Opus Streaming ===")

    # 创建WAV文件（ffmpeg可以很好地处理WAV格式）
    create_test_wav("music.wav", duration=5, frequency=440)
    create_test_wav("default.wav", duration=3, frequency=523)
    create_test_wav("test.wav", duration=2, frequency=659)

    # 创建一个更有趣的音乐文件
    create_test_music_wav()

    print("=== Test Audio Files Created Successfully ===")
    print("These files will be converted to Opus format by ffmpeg during streaming.")

    # 列出创建的文件
    audio_dir = "audio"
    if os.path.exists(audio_dir):
        print(f"\nFiles in {audio_dir}:")
        for file in sorted(os.listdir(audio_dir)):
            filepath = os.path.join(audio_dir, file)
            size = os.path.getsize(filepath)
            duration = size / (16000 * 2)  # 估算时长（16kHz, 16bit）
            print(f"  {file} ({size} bytes, ~{duration:.1f}s)")

    print(f"\nNote: Make sure ffmpeg is installed and available in PATH")
    print(f"Test command: ffmpeg -version")

if __name__ == "__main__":
    main()
